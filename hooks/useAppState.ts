"use client";

import { useState, useEffect } from "react";
import { AppState, DeploymentConfig, GeneratedConfig, ChannelAnalysisResult, StepType } from "@/lib/types";

const STORAGE_KEY = "discord-deployer-state";

export function useAppState() {
  const [state, setState] = useState<AppState>({
    currentStep: "intro",
    deploymentConfig: {
      creatorType: "",
      audienceType: "",
      contentStyle: "",
      features: [],
      customRequirements: "",
    },
    generatedConfig: null,
    isGenerating: false,
    isDeploying: false,
    deploymentResult: null,
    deploymentError: null,
    channelAnalysis: null,
    isAnalyzing: false,
    useRecommendation: false,
    isDebug: false,
    errorDetails: null,
  });

  // 从localStorage加载状态
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsedState = JSON.parse(saved);
        setState(prevState => ({
          ...prevState,
          ...parsedState,
          // 重置临时状态
          isGenerating: false,
          isDeploying: false,
          isAnalyzing: false,
        }));
      }
    } catch (error) {
      console.error("Failed to load state from localStorage:", error);
    }
  }, []);

  // 保存状态到localStorage
  const saveState = (newState: Partial<AppState>) => {
    const updatedState = { ...state, ...newState };
    setState(updatedState);
    
    try {
      // 只保存需要持久化的状态
      const stateToSave = {
        currentStep: updatedState.currentStep,
        deploymentConfig: updatedState.deploymentConfig,
        generatedConfig: updatedState.generatedConfig,
        deploymentResult: updatedState.deploymentResult,
        channelAnalysis: updatedState.channelAnalysis,
        useRecommendation: updatedState.useRecommendation,
        isDebug: updatedState.isDebug,
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error("Failed to save state to localStorage:", error);
    }
  };

  // 更新当前步骤
  const setCurrentStep = (step: StepType) => {
    saveState({ currentStep: step });
  };

  // 更新部署配置
  const setDeploymentConfig = (config: DeploymentConfig) => {
    saveState({ deploymentConfig: config });
  };

  // 设置生成状态
  const setGenerating = (isGenerating: boolean) => {
    setState(prev => ({ ...prev, isGenerating }));
  };

  // 设置生成的配置
  const setGeneratedConfig = (config: GeneratedConfig | null) => {
    saveState({ generatedConfig: config });
  };

  // 设置部署状态
  const setDeploying = (isDeploying: boolean) => {
    setState(prev => ({ ...prev, isDeploying }));
  };

  // 设置部署结果
  const setDeploymentResult = (result: any) => {
    saveState({ deploymentResult: result, deploymentError: null });
  };

  // 设置部署错误
  const setDeploymentError = (error: string | null) => {
    saveState({ deploymentError: error });
  };

  // 设置频道分析状态
  const setAnalyzing = (isAnalyzing: boolean) => {
    setState(prev => ({ ...prev, isAnalyzing }));
  };

  // 设置频道分析结果
  const setChannelAnalysis = (analysis: ChannelAnalysisResult | null) => {
    saveState({ channelAnalysis: analysis });
  };

  // 切换推荐配置使用
  const toggleRecommendation = () => {
    saveState({ useRecommendation: !state.useRecommendation });
  };

  // 切换调试模式
  const toggleDebug = () => {
    saveState({ isDebug: !state.isDebug });
  };

  // 设置错误详情
  const setErrorDetails = (details: string | null) => {
    setState(prev => ({ ...prev, errorDetails: details }));
  };

  // 重置状态
  const resetState = () => {
    const initialState: AppState = {
      currentStep: "intro",
      deploymentConfig: {
        creatorType: "",
        audienceType: "",
        contentStyle: "",
        features: [],
        customRequirements: "",
      },
      generatedConfig: null,
      isGenerating: false,
      isDeploying: false,
      deploymentResult: null,
      deploymentError: null,
      channelAnalysis: null,
      isAnalyzing: false,
      useRecommendation: false,
      isDebug: false,
      errorDetails: null,
    };
    
    setState(initialState);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error("Failed to clear localStorage:", error);
    }
  };

  // 检查是否可以进入下一步
  const canProceedToStep = (step: StepType): boolean => {
    switch (step) {
      case "intro":
        return true;
      case "config":
        return true;
      case "preview":
        return !!(state.deploymentConfig.creatorType && state.deploymentConfig.audienceType);
      case "deploy":
        return !!state.generatedConfig;
      default:
        return false;
    }
  };

  return {
    state,
    setCurrentStep,
    setDeploymentConfig,
    setGenerating,
    setGeneratedConfig,
    setDeploying,
    setDeploymentResult,
    setDeploymentError,
    setAnalyzing,
    setChannelAnalysis,
    toggleRecommendation,
    toggleDebug,
    setErrorDetails,
    resetState,
    canProceedToStep,
  };
}
