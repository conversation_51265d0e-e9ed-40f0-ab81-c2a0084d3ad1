#!/bin/bash

echo "🚀 启动 Discord Community Builder"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 Node.js 版本
echo -e "${BLUE}检查 Node.js 版本...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js${NC}"
    exit 1
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js 版本: $NODE_VERSION${NC}"

# 检查 pnpm 版本
echo -e "${BLUE}检查 pnpm 版本...${NC}"
if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}❌ pnpm 未安装，请先安装 pnpm${NC}"
    echo "安装命令: npm install -g pnpm"
    exit 1
fi

PNPM_VERSION=$(pnpm --version)
echo -e "${GREEN}✅ pnpm 版本: $PNPM_VERSION${NC}"

# 检查环境变量文件
echo -e "${BLUE}检查环境变量配置...${NC}"
if [ ! -f ".env.local" ]; then
    echo -e "${RED}❌ 错误: .env.local 文件不存在${NC}"
    echo -e "${YELLOW}正在创建示例环境变量文件...${NC}"
    cat > .env.local << 'EOL'
# Discord 配置 (必需)
DISCORD_CLIENT_ID=你的应用程序ID
DISCORD_CLIENT_SECRET=你的客户端密钥
DISCORD_BOT_TOKEN=你的Bot令牌

# OpenRouter AI 配置 (必需)
OPENROUTER_API_KEY=你的OpenRouter_API密钥

# 前端配置 (推荐)
NEXT_PUBLIC_DISCORD_CLIENT_ID=你的应用程序ID
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Bot服务配置 (可选，用于增强安全性)
BOT_SERVICE_URL=http://localhost:3001
BOT_SERVICE_TOKEN=your_secure_random_token_here_123456
PORT=3001

# 开发配置 (可选)
NODE_ENV=development
EOL
    echo -e "${GREEN}✅ 已创建示例 .env.local 文件${NC}"
    echo -e "${YELLOW}⚠️ 请编辑此文件并填入您的实际配置后再运行${NC}"
    echo ""
    echo -e "${BLUE}配置指南:${NC}"
    echo "1. 访问 https://discord.com/developers/applications 创建Discord应用"
    echo "2. 访问 https://openrouter.ai 获取OpenRouter API密钥"
    echo "3. 编辑 .env.local 文件，填入实际的配置值"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ .env.local 文件存在${NC}"

# 验证关键环境变量
echo -e "${BLUE}验证环境变量配置...${NC}"
source .env.local

MISSING_VARS=()

if [ -z "$DISCORD_CLIENT_ID" ] || [ "$DISCORD_CLIENT_ID" = "你的应用程序ID" ]; then
    MISSING_VARS+=("DISCORD_CLIENT_ID")
fi

if [ -z "$DISCORD_CLIENT_SECRET" ] || [ "$DISCORD_CLIENT_SECRET" = "你的客户端密钥" ]; then
    MISSING_VARS+=("DISCORD_CLIENT_SECRET")
fi

if [ -z "$DISCORD_BOT_TOKEN" ] || [ "$DISCORD_BOT_TOKEN" = "你的Bot令牌" ]; then
    MISSING_VARS+=("DISCORD_BOT_TOKEN")
fi

if [ -z "$OPENROUTER_API_KEY" ] || [ "$OPENROUTER_API_KEY" = "你的OpenRouter_API密钥" ]; then
    MISSING_VARS+=("OPENROUTER_API_KEY")
fi

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo -e "${RED}❌ 以下环境变量未正确配置:${NC}"
    for var in "${MISSING_VARS[@]}"; do
        echo -e "${RED}  - $var${NC}"
    done
    echo ""
    echo -e "${YELLOW}请编辑 .env.local 文件并填入正确的配置值${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境变量配置验证通过${NC}"

# 安装依赖
echo -e "${BLUE}📦 安装依赖包...${NC}"
pnpm install

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 依赖安装失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 依赖安装完成${NC}"

# 检查端口是否被占用
echo -e "${BLUE}🔍 检查端口状态...${NC}"
if command -v lsof &> /dev/null; then
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 3000 已被占用，请先关闭占用该端口的进程${NC}"
        echo "可以使用命令: lsof -ti:3000 | xargs kill -9"
        exit 1
    fi

    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 3001 已被占用，请先关闭占用该端口的进程${NC}"
        echo "可以使用命令: lsof -ti:3001 | xargs kill -9"
        exit 1
    fi
    echo -e "${GREEN}✅ 端口 3000 和 3001 可用${NC}"
else
    echo -e "${YELLOW}⚠️  无法检查端口状态 (lsof 命令不可用)${NC}"
fi

echo ""
echo -e "${GREEN}🎉 准备完成！您想如何启动应用？${NC}"
echo ""
echo -e "${BLUE}1)${NC} 自动启动前端应用 (推荐)"
echo -e "${BLUE}2)${NC} 自动启动前端和机器人服务"
echo -e "${BLUE}3)${NC} 只显示启动命令（手动启动）"
echo ""
read -p "请选择 [1/2/3]: " choice

case $choice in
    1)
        echo -e "${BLUE}正在启动前端应用...${NC}"
        echo -e "${YELLOW}注意: 新架构下，机器人服务已集成到前端应用中${NC}"

        # 启动前端应用
        pnpm run dev &
        FRONTEND_PID=$!
        echo -e "${GREEN}前端应用已启动，PID: $FRONTEND_PID${NC}"

        echo ""
        echo -e "${GREEN}✅ 应用已成功启动！${NC}"
        echo -e "${BLUE}- 前端应用: http://localhost:3000${NC}"
        echo -e "${BLUE}- API服务: http://localhost:3000/api${NC}"
        echo ""
        echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"

        # 等待用户中断
        wait
        ;;
    2)
        echo -e "${BLUE}正在启动前端应用...${NC}"
        # 启动前端应用，在后台运行
        pnpm run dev &
        FRONTEND_PID=$!
        echo -e "${GREEN}前端应用已启动，PID: $FRONTEND_PID${NC}"

        # 等待前端应用启动
        echo -e "${BLUE}等待前端应用启动...${NC}"
        sleep 3

        echo -e "${BLUE}正在启动机器人服务...${NC}"
        # 启动机器人服务
        pnpm run bot &
        BOT_PID=$!
        echo -e "${GREEN}机器人服务已启动，PID: $BOT_PID${NC}"

        echo ""
        echo -e "${GREEN}✅ 应用已成功启动！${NC}"
        echo -e "${BLUE}- 前端应用: http://localhost:3000${NC}"
        echo -e "${BLUE}- 机器人服务: http://localhost:3001${NC}"
        echo ""
        echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"

        # 等待用户中断
        wait
        ;;
    3)
        echo ""
        echo -e "${BLUE}请按以下步骤手动启动应用：${NC}"
        echo ""
        echo -e "${GREEN}推荐方式 (单服务):${NC}"
        echo -e "${BLUE}1️⃣  运行:${NC} pnpm run dev"
        echo -e "${BLUE}2️⃣  访问:${NC} http://localhost:3000"
        echo ""
        echo -e "${YELLOW}传统方式 (双服务):${NC}"
        echo -e "${BLUE}1️⃣  在第一个终端运行:${NC} pnpm run dev"
        echo -e "${BLUE}2️⃣  在第二个终端运行:${NC} pnpm run bot"
        echo -e "${BLUE}3️⃣  访问:${NC} http://localhost:3000"
        echo ""
        ;;
    *)
        echo -e "${RED}无效选择，退出${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}==================================${NC}"
