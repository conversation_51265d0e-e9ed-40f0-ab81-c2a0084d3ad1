#!/bin/bash

echo "🚀 启动 Discord Community Builder"
echo "=================================="

# 检查 Node.js 版本
echo "检查 Node.js 版本..."
node --version

# 检查 pnpm 版本
echo "检查 pnpm 版本..."
pnpm --version

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "❌ 错误: .env.local 文件不存在"
    echo "正在创建示例环境变量文件..."
    cat > .env.local << 'EOL'
# Discord 配置
DISCORD_CLIENT_ID=你的应用程序ID
DISCORD_CLIENT_SECRET=你的客户端密钥
NEXT_PUBLIC_DISCORD_CLIENT_ID=你的应用程序ID
DISCORD_BOT_TOKEN=你的Bot令牌

# 应用程序配置
NEXT_PUBLIC_BASE_URL=http://localhost:3000
BOT_SERVICE_URL=http://localhost:3001
BOT_SERVICE_TOKEN=your_secure_random_token_here_123456
PORT=3001
EOL
    echo "✅ 已创建示例 .env.local 文件，请编辑此文件并填入您的实际配置"
    echo "⚠️ 在填入正确的配置前，应用将无法正常工作"
    exit 1
fi

echo "✅ .env.local 文件存在"

# 安装依赖
echo "📦 安装依赖包..."
pnpm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 检查端口是否被占用
echo "🔍 检查端口状态..."
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 3000 已被占用，请先关闭占用该端口的进程"
    exit 1
fi

if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 3001 已被占用，请先关闭占用该端口的进程"
    exit 1
fi

echo ""
echo "🎉 准备完成！您想如何启动应用？"
echo ""
echo "1) 自动启动前端和机器人服务"
echo "2) 只显示启动命令（手动启动）"
echo ""
read -p "请选择 [1/2]: " choice

if [ "$choice" = "1" ]; then
    echo "正在启动前端应用..."
    # 启动前端应用，在后台运行
    pnpm run dev &
    FRONTEND_PID=$!
    echo "前端应用已启动，PID: $FRONTEND_PID"
    
    # 等待前端应用启动
    echo "等待前端应用启动..."
    sleep 3
    
    echo "正在启动机器人服务..."
    # 启动机器人服务
    pnpm run bot &
    BOT_PID=$!
    echo "机器人服务已启动，PID: $BOT_PID"
    
    echo ""
    echo "✅ 应用已成功启动！"
    echo "- 前端应用: http://localhost:3000"
    echo "- 机器人服务: http://localhost:3001"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    wait
else
    echo ""
    echo "请按以下步骤手动启动应用："
    echo ""
    echo "1️⃣  在第一个终端窗口运行:"
    echo "   pnpm run dev"
    echo ""
    echo "2️⃣  在第二个终端窗口运行:"
    echo "   pnpm run bot"
    echo ""
    echo "3️⃣  打开浏览器访问:"
    echo "   http://localhost:3000"
    echo ""
fi

echo "=================================="
