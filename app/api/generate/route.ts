import { NextResponse } from "next/server";

// Use fetch to directly call OpenRouter API instead of OpenAI SDK
const API_URL = "https://openrouter.ai/api/v1/chat/completions";

export async function POST(request: Request) {
  try {
    const { creatorType, audienceType, contentStyle, features, customRequirements } = await request.json();
    
    if (!creatorType || !audienceType) {
      return NextResponse.json(
        { error: "Missing required parameters: creator type and audience type" },
        { status: 400 }
      );
    }

    // Build prompt in English to avoid ByteString errors with Chinese characters
    const prompt = `
    Design a Discord server structure for a creator of type "${creatorType}" whose main audience is "${audienceType}".
    
    Content style: ${contentStyle || "Not specified"}
    
    Features to include: ${features?.join(", ") || "Standard features"}
    
    Special requirements: ${customRequirements || "None"}
    
    Please generate the following:
    1. A list of channels, including name, type (text/voice/announcement), position order, whether public, and description
    2. A welcome message
    3. Community rules list
    4. Channel category suggestions
    
    Return in JSON format with the following structure:
    {
      "channels": [
        {
          "name": "channel name",
          "type": "text|voice|announcement",
          "position": 0,
          "isPublic": true,
          "description": "channel description"
        }
      ],
      "welcomeMessage": "welcome message text...",
      "rules": ["rule 1", "rule 2", ...],
      "categories": ["category 1", "category 2", ...]
    }
    `;

    // Call OpenRouter API
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`,
        "HTTP-Referer": process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000", 
        "X-Title": "Discord AI Community Builder"
      },
      body: JSON.stringify({
        model: "deepseek/deepseek-chat-v3-0324:free",
        messages: [
          { role: "system", content: "You are a Discord community structure expert, skilled at designing optimal Discord server structures for creators. Please respond in JSON format." },
          { role: "user", content: prompt }
        ],
        temperature: 0.7,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("OpenRouter API error:", errorData);
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    const responseContent = result.choices[0].message.content;
    
    if (!responseContent) {
      throw new Error("API returned an empty response");
    }

    try {
      // Parse JSON response
      const generatedConfig = JSON.parse(responseContent);
      
      // Validate the generated config format
      if (!generatedConfig.channels || !generatedConfig.welcomeMessage || !generatedConfig.rules) {
        throw new Error("Generated configuration has incorrect format");
      }
      
      return NextResponse.json(generatedConfig);
    } catch (parseError) {
      console.error("Failed to parse API response:", parseError);
      console.log("Original response:", responseContent);
      throw new Error("Unable to parse AI-generated configuration");
    }
  } catch (error: any) {
    console.error("Error generating configuration:", error);
    return NextResponse.json(
      { error: error.message || "Error occurred while generating configuration" },
      { status: 500 }
    );
  }
} 