import { NextResponse } from "next/server"

// OpenRouter API URL
const API_URL = "https://openrouter.ai/api/v1/chat/completions";

// Basic FAQ responses for fallback
const FAQ_RESPONSES: Record<string, string> = {
  // About Discord server creation
  "创建服务器": "您可以使用我们的AI工具快速创建一个定制的Discord服务器。只需回答几个关于您的内容和受众的问题，我们就会生成最适合您的频道结构。",
  "频道结构": "根据您的创作者类型和内容风格，我们会推荐适合您社区的频道结构，包括欢迎频道、规则频道、通知频道、内容讨论区等。",
  
  // About permissions
  "权限": "要使用我们的工具，您需要是Discord服务器的管理员或拥有者，并具有'管理服务器'、'管理频道'和'管理角色'权限。在授权过程中，我们会自动检查这些权限。",
  "授权": "我们使用Discord的官方OAuth2授权流程，这确保了您的账户安全。我们只请求创建和管理频道所需的最小权限。",
  
  // About management and customization
  "删除频道": "目前，您可以通过Discord直接删除已创建的频道。在未来的更新中，我们计划添加直接从平台删除频道的功能。",
  "自定义": "您可以在预览阶段查看AI生成的频道配置。部署后，您可以通过Discord直接编辑频道名称、描述、权限等。",
  
  // About deployment issues
  "部署错误": "如果在部署过程中遇到错误，通常是由于权限不足或Discord API限制。请确认您有足够的权限，并且没有超过Discord的API速率限制。",
  "撤销部署": "目前，部署操作无法自动撤销。如果需要恢复更改，您需要手动删除已创建的频道。我们正在开发撤销功能。",
  
  // About features and usage
  "欢迎消息": "我们的AI会基于您的内容风格生成一条欢迎消息，您可以在预览页面查看并在部署后通过Discord编辑。",
  "社区规则": "基于您的社区类型，我们会自动生成一套适合的社区规则，您可以根据需要进行修改。",
}

// Check if message contains specific keywords
function findRelevantResponse(message: string): string | null {
  // Convert to lowercase for case-insensitive matching
  const lowerMessage = message.toLowerCase();
  
  for (const [keyword, response] of Object.entries(FAQ_RESPONSES)) {
    if (lowerMessage.includes(keyword.toLowerCase())) {
      return response;
    }
  }
  
  return null;
}

export async function POST(request: Request) {
  try {
    const { message } = await request.json();
    
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: "消息必须是非空字符串" },
        { status: 400 }
      );
    }
    
    // Check if we need to use the fallback
    const useLocalFallback = !process.env.OPENROUTER_API_KEY || process.env.USE_LOCAL_CHAT_FALLBACK === 'true';
    
    if (useLocalFallback) {
      // Find matching preset reply
      const relevantResponse = findRelevantResponse(message);
      
      if (relevantResponse) {
        return NextResponse.json({ response: relevantResponse });
      }
      
      // Default fallback response
      return NextResponse.json({
        response: "感谢您的问题！我是您的AI助手，可以帮助您了解如何使用我们的Discord社区构建工具。您可以询问关于创建服务器、频道结构、授权过程或自定义选项的问题。"
      });
    }
    
    // If we have an API key, use OpenRouter with DeepSeek model
    try {
      // Create a system prompt with context about the Discord tool
      const systemPrompt = `You are a helpful assistant for a Discord server creation tool. Your role is to help users understand how to use the tool to create and customize Discord servers with AI-generated configurations. Always respond in Chinese. Be concise, helpful, and focus on answering the user's questions about the Discord server builder tool.`;
      
      // Create a user message with the user's question
      const userMessage = message;
      
      const response = await fetch(API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`,
          "HTTP-Referer": process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
          "X-Title": "Discord AI Community Builder - Chat Assistant"
        },
        body: JSON.stringify({
          model: "deepseek/deepseek-chat-v3-0324:free",
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: userMessage }
          ],
          temperature: 0.7,
          max_tokens: 800
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error("OpenRouter API Error:", errorData);
        throw new Error(`API call failed: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      const aiResponse = result.choices[0].message.content;
      
      return NextResponse.json({ response: aiResponse });
    } catch (apiError) {
      console.error("AI API Error:", apiError);
      
      // Use fallback if API fails
      const fallbackResponse = findRelevantResponse(message) || 
        "抱歉，我现在无法连接到AI服务。请尝试一个更具体的问题，或者稍后再试。";
      
      return NextResponse.json({ response: fallbackResponse });
    }
    
  } catch (error) {
    console.error("Chat API Error:", error);
    return NextResponse.json(
      { error: "处理请求时出错" },
      { status: 500 }
    );
  }
} 