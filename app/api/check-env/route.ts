import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const requiredVars = [
      'DISCORD_BOT_TOKEN',
      'DISCORD_CLIENT_ID', 
      'DISCORD_CLIENT_SECRET',
      'OPENROUTER_API_KEY',
    ];

    const optionalVars = [
      'NEXT_PUBLIC_DISCORD_CLIENT_ID',
      'NEXT_PUBLIC_APP_URL',
      'BOT_SERVICE_TOKEN',
      'BOT_SERVICE_URL',
    ];

    const missingRequired = requiredVars.filter(varName => !process.env[varName]);
    const missingOptional = optionalVars.filter(varName => !process.env[varName]);

    const envStatus = {
      isConfigured: missingRequired.length === 0,
      missingVars: missingRequired,
      missingOptionalVars: missingOptional,
      configuredVars: requiredVars.filter(varName => !!process.env[varName]),
      recommendations: []
    };

    // 添加配置建议
    if (missingRequired.length > 0) {
      envStatus.recommendations.push(
        "请在项目根目录创建 .env.local 文件并配置必需的环境变量"
      );
    }

    if (missingOptional.includes('NEXT_PUBLIC_DISCORD_CLIENT_ID')) {
      envStatus.recommendations.push(
        "建议配置 NEXT_PUBLIC_DISCORD_CLIENT_ID 以启用前端Discord集成"
      );
    }

    if (missingOptional.includes('BOT_SERVICE_TOKEN')) {
      envStatus.recommendations.push(
        "建议配置 BOT_SERVICE_TOKEN 以增强API安全性"
      );
    }

    return NextResponse.json(envStatus);
  } catch (error) {
    console.error('Environment check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check environment variables',
        isConfigured: false,
        missingVars: ['检查失败']
      },
      { status: 500 }
    );
  }
}
