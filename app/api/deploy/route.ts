import { type NextRequest, NextResponse } from "next/server"
import { ChannelType, TextChannel, PermissionFlagsBits } from "discord.js"
import { getDiscordClient, getDiscordChannelType, determineChannelCategory, type DeploymentConfig } from "@/lib/discord-client"
import { AppError, handleError, logError, ErrorCode, getUserFriendlyMessage } from "@/lib/errors"

interface DeploymentRequest {
  config: DeploymentConfig
  userConfig: {
    creatorType: string
    audienceType: string
    contentStyle: string
    features: string[]
    customRequirements: string
  }
}

// 登录客户端
if (!clientReady) {
  client.login(process.env.DISCORD_BOT_TOKEN).catch(err => {
    console.error("Failed to login to Discord:", err)
  })
}

// 简单的日志工具
const logInfo = (message: string) => {
  console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
};

const logError = (message: string, error?: any) => {
  console.error(`[ERROR] ${new Date().toISOString()}: ${message}`);
  if (error) {
    console.error(error);
  }
};

// 速率限制重试函数
async function createWithRateLimit<T>(
  createFn: () => Promise<T>,
  retryCount: number = 3,
  delay: number = 500
): Promise<T> {
  let attempts = 0;

  while (attempts < retryCount) {
    try {
      return await createFn();
    } catch (error: any) {
      attempts++;
      if (error.code === 429) { // Rate limit error
        const retryAfter = error.retry_after || delay;
        console.log(`Rate limited. Waiting ${retryAfter}ms before retry. Attempt ${attempts}/${retryCount}`);
        await new Promise(resolve => setTimeout(resolve, retryAfter));
      } else if (attempts >= retryCount) {
        throw error;
      } else {
        console.log(`Error occurred, retrying. Attempt ${attempts}/${retryCount}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(`Failed after ${retryCount} attempts`);
}

export async function POST(request: NextRequest) {
  try {
    const body: DeploymentRequest = await request.json()
    const accessToken = request.cookies.get("discord_access_token")?.value

    console.log(`部署请求开始处理，配置项: ${JSON.stringify(body.config.categories)}`);

    if (!accessToken) {
      throw AppError.auth("用户未授权，请先进行Discord授权");
    }

    // 获取Discord客户端
    const client = await getDiscordClient();

    // Get user's guilds to find the target guild
    console.log(`正在获取用户的Discord服务器列表...`);

    const guildsResponse = await fetch("https://discord.com/api/users/@me/guilds", {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!guildsResponse.ok) {
      const errorData = await guildsResponse.json().catch(() => ({}));
      throw AppError.discord(
        `获取Discord服务器列表失败: ${guildsResponse.status} ${guildsResponse.statusText}`,
        ErrorCode.DISCORD_AUTH_FAILED,
        { status: guildsResponse.status, error: errorData }
      );
    }

    const guilds = await guildsResponse.json()
    console.log(`成功获取到${guilds.length}个服务器`);

    // Find the first guild where the user has admin permissions
    const targetGuild = guilds.find(
      (guild: any) =>
        (guild.permissions & 0x8) === 0x8 || // Administrator
        (guild.permissions & 0x10) === 0x10, // Manage Channels
    )

    if (!targetGuild) {
      throw AppError.discord(
        "未找到合适的Discord服务器，您需要在至少一个服务器中拥有管理员权限",
        ErrorCode.DISCORD_INSUFFICIENT_PERMISSIONS,
        {
          availableGuilds: guilds.map((g: any) => ({
            id: g.id,
            name: g.name,
            permissions: g.permissions
          }))
        }
      );
    }

    console.log(`选择目标服务器: ${targetGuild.name} (${targetGuild.id})`);

    // 获取目标服务器
    const guild = await client.guilds.fetch(targetGuild.id)
    if (!guild) {
      throw AppError.notFound("找不到目标Discord服务器", { guildId: targetGuild.id });
    }

    // 验证机器人权限
    const botMember = await guild.members.fetch(client.user!.id);
    if (!botMember.permissions.has(PermissionFlagsBits.ManageChannels)) {
      throw AppError.discord(
        "机器人缺少管理频道的权限",
        ErrorCode.DISCORD_INSUFFICIENT_PERMISSIONS,
        { requiredPermissions: ["MANAGE_CHANNELS"] }
      );
    }

    // 获取现有频道
    const existingChannels = await guild.channels.fetch()
    const existingChannelNames = new Map()
    existingChannels.forEach(channel => {
      if (channel) {
        existingChannelNames.set(channel.name.toLowerCase(), channel)
      }
    })

    console.log(`Found ${existingChannelNames.size} existing channels in guild ${guild.name}`);

    // 创建频道分类
    const categoryMap = new Map()
    if (body.config.categories && body.config.categories.length > 0) {
      for (const categoryName of body.config.categories) {
        // 检查是否已存在同名分类
        const existingCategory = existingChannelNames.get(categoryName.toLowerCase())
        if (existingCategory && existingCategory.type === ChannelType.GuildCategory) {
          categoryMap.set(categoryName, existingCategory)
          console.log(`Using existing category: ${categoryName}`)
        } else {
          // 创建新分类
          try {
            const category = await createWithRateLimit(async () => {
              return await guild.channels.create({
                name: categoryName,
                type: ChannelType.GuildCategory,
              });
            });
            categoryMap.set(categoryName, category)
            console.log(`Created new category: ${categoryName}`)
          } catch (err) {
            console.error(`Failed to create category ${categoryName}:`, err)
          }
        }
      }
    }

    // 创建频道
    const createdChannels = []

    for (const channelConfig of body.config.channels) {
      // 检查是否已存在同名频道
      const existingChannel = existingChannelNames.get(channelConfig.name.toLowerCase())
      if (existingChannel) {
        console.log(`Channel ${channelConfig.name} already exists, skipping creation`)
        createdChannels.push({
          id: existingChannel.id,
          name: existingChannel.name,
          existing: true
        })
        continue
      }

      // 确定频道类型
      const channelType = getDiscordChannelType(channelConfig.type);

      // 确定频道所属分类
      const parentId = determineChannelCategory(channelConfig.name, categoryMap);

      // 创建频道
      try {
        const channelOptions: any = {
          name: channelConfig.name,
          type: channelType,
          parent: parentId,
          topic: channelConfig.description,
          position: channelConfig.position,
        };

        // 设置私有频道权限
        if (!channelConfig.isPublic) {
          channelOptions.permissionOverwrites = [
            {
              id: guild.roles.everyone.id,
              deny: [PermissionFlagsBits.ViewChannel],
            },
          ];
        }

        const channel = await createWithRateLimit(async () => {
          return await guild.channels.create(channelOptions);
        });

        createdChannels.push({
          id: channel.id,
          name: channel.name,
          type: channelConfig.type,
          existing: false
        })

        console.log(`Created channel: ${channel.name}`)

        // 如果是欢迎频道，发送欢迎消息
        if (channelConfig.name.toLowerCase().includes("welcome") && body.config.welcomeMessage && channel.type === ChannelType.GuildText) {
          try {
            await (channel as TextChannel).send(body.config.welcomeMessage)
            console.log(`Sent welcome message to ${channel.name}`)
          } catch (err) {
            console.error(`Failed to send welcome message:`, err)
          }
        }

        // 如果是规则频道，发送规则
        if (channelConfig.name.toLowerCase().includes("rule") && body.config.rules && body.config.rules.length > 0 && channel.type === ChannelType.GuildText) {
          try {
            const rulesText = "**社区规则**\n\n" + body.config.rules.map((rule, index) => `${index + 1}. ${rule}`).join("\n\n")
            await (channel as TextChannel).send(rulesText)
            console.log(`Sent rules to ${channel.name}`)
          } catch (err) {
            console.error(`Failed to send rules:`, err)
          }
        }

        // 添加延迟避免速率限制
        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (err) {
        console.error(`Failed to create channel ${channelConfig.name}:`, err)
      }
    }

    return NextResponse.json({
      success: true,
      result: {
        guildId: targetGuild.id,
        guildName: guild.name,
        channelsCreated: createdChannels.filter(c => !c.existing).length,
        channelsSkipped: createdChannels.filter(c => c.existing).length,
        channels: createdChannels
      }
    })

  } catch (error: unknown) {
    const appError = handleError(error);
    logError(appError, {
      endpoint: '/api/deploy',
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: getUserFriendlyMessage(appError),
        code: appError.code,
        ...(process.env.NODE_ENV === 'development' && {
          details: appError.context,
          stack: appError.stack
        })
      },
      { status: appError.statusCode }
    );
  }
}
