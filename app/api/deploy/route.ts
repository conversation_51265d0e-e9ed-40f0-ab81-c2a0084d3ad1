import { type NextRequest, NextResponse } from "next/server"
import { Client, GatewayIntentBits, ChannelType, TextChannel } from "discord.js"

interface DeploymentRequest {
  config: {
    channels: Array<{
      name: string
      type: "text" | "voice" | "announcement"
      position: number
      isPublic: boolean
      description: string
    }>
    welcomeMessage: string
    rules: string[]
    categories: string[]
  }
  userConfig: {
    creatorType: string
    audienceType: string
    contentStyle: string
    features: string[]
    customRequirements: string
  }
}

// 初始化Discord客户端
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
  ],
})

// 确保客户端已登录
let clientReady = false
client.on('ready', () => {
  console.log(`Discord bot logged in as ${client.user?.tag}`)
  clientReady = true
})

// 登录客户端
if (!clientReady) {
  client.login(process.env.DISCORD_BOT_TOKEN).catch(err => {
    console.error("Failed to login to Discord:", err)
  })
}

// 简单的日志工具
const logInfo = (message: string) => {
  console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
};

const logError = (message: string, error?: any) => {
  console.error(`[ERROR] ${new Date().toISOString()}: ${message}`);
  if (error) {
    console.error(error);
  }
};

export async function POST(request: NextRequest) {
  try {
    const body: DeploymentRequest = await request.json()
    const accessToken = request.cookies.get("discord_access_token")?.value

    logInfo(`部署请求开始处理，配置项: ${JSON.stringify(body.config.categories)}`);

    if (!accessToken) {
      logError("未授权访问 - 缺少访问令牌");
      return NextResponse.json({ error: "Not authorized" }, { status: 401 })
    }

    // 确保Discord机器人已登录
    if (!client.isReady()) {
      await new Promise<void>((resolve) => {
        const checkReady = () => {
          if (client.isReady()) {
            resolve();
          } else {
            setTimeout(checkReady, 100);
          }
        };
        checkReady();
      });
    }

    // Get user's guilds to find the target guild
    logInfo(`正在获取用户的Discord服务器列表...`);
    
    try {
      const guildsResponse = await fetch("https://discord.com/api/users/@me/guilds", {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })

      if (!guildsResponse.ok) {
        const errorData = await guildsResponse.json().catch(() => ({}));
        logError(`获取服务器列表失败: ${guildsResponse.status} ${guildsResponse.statusText}`, errorData);
        return NextResponse.json({ 
          error: "Failed to fetch guilds", 
          details: {
            status: guildsResponse.status,
            statusText: guildsResponse.statusText,
            error: errorData
          }
        }, { status: 400 })
      }

      const guilds = await guildsResponse.json()
      logInfo(`成功获取到${guilds.length}个服务器`);

      // For demo purposes, we'll use the first guild where the user has admin permissions
      const targetGuild = guilds.find(
        (guild: any) =>
          (guild.permissions & 0x8) === 0x8 || // Administrator
          (guild.permissions & 0x10) === 0x10, // Manage Channels
      )

      if (!targetGuild) {
        logError(`未找到合适的服务器 - 用户没有管理员权限的服务器`);
        return NextResponse.json({ 
          error: "No suitable guild found", 
          details: {
            message: "User doesn't have administrator permissions in any guild",
            availableGuilds: guilds.map((g: any) => ({ 
              id: g.id, 
              name: g.name, 
              permissions: g.permissions 
            }))
          }
        }, { status: 400 })
      }

      logInfo(`选择目标服务器: ${targetGuild.name} (${targetGuild.id})`);

      // 获取目标服务器
      const guild = await client.guilds.fetch(targetGuild.id)
      if (!guild) {
        logError(`找不到目标服务器`);
        return NextResponse.json({ error: "找不到目标服务器" }, { status: 404 })
      }

      // 获取现有频道
      const existingChannels = await guild.channels.fetch()
      const existingChannelNames = new Map()
      existingChannels.forEach(channel => {
        if (channel) {
          existingChannelNames.set(channel.name.toLowerCase(), channel)
        }
      })
      
      logInfo(`Found ${existingChannelNames.size} existing channels in guild ${guild.name}`);

      // 创建频道分类
      const categoryMap = new Map()
      if (body.config.categories && body.config.categories.length > 0) {
        for (const categoryName of body.config.categories) {
          // 检查是否已存在同名分类
          const existingCategory = existingChannelNames.get(categoryName.toLowerCase())
          if (existingCategory && existingCategory.type === ChannelType.GuildCategory) {
            categoryMap.set(categoryName, existingCategory)
            logInfo(`Using existing category: ${categoryName}`)
          } else {
            // 创建新分类
            try {
              const category = await guild.channels.create({
                name: categoryName,
                type: ChannelType.GuildCategory,
              })
              categoryMap.set(categoryName, category)
              logInfo(`Created new category: ${categoryName}`)
            } catch (err) {
              logError(`Failed to create category ${categoryName}:`, err)
            }
          }
        }
      }

      // 创建频道
      const createdChannels = []
      let categoryIndex = 0
      
      for (const channelConfig of body.config.channels) {
        // 检查是否已存在同名频道
        const existingChannel = existingChannelNames.get(channelConfig.name.toLowerCase())
        if (existingChannel) {
          logInfo(`Channel ${channelConfig.name} already exists, skipping creation`)
          createdChannels.push({
            id: existingChannel.id,
            name: existingChannel.name,
            existing: true
          })
          continue
        }
        
        // 确定频道类型
        let channelType;
        switch (channelConfig.type) {
          case "text":
            channelType = ChannelType.GuildText;
            break;
          case "voice":
            channelType = ChannelType.GuildVoice;
            break;
          case "announcement":
            channelType = ChannelType.GuildAnnouncement;
            break;
          default:
            channelType = ChannelType.GuildText;
        }

        // 确定频道所属分类
        let parentId = null
        if (body.config.categories && body.config.categories.length > 0) {
          // 使用 position 来确定分类，或者循环使用分类
          const categoryName = body.config.categories[categoryIndex % body.config.categories.length]
          const category = categoryMap.get(categoryName)
          if (category) {
            parentId = category.id
          }
          categoryIndex++
        }

        // 创建频道
        try {
          const channel = await guild.channels.create({
            name: channelConfig.name,
            type: channelType as ChannelType.GuildText | ChannelType.GuildVoice | ChannelType.GuildAnnouncement,
            parent: parentId,
            topic: channelConfig.description
          })
          
          createdChannels.push({
            id: channel.id,
            name: channel.name,
            existing: false
          })
          
          logInfo(`Created channel: ${channel.name}`)
          
          // 如果是欢迎频道，发送欢迎消息
          if (channelConfig.name.toLowerCase().includes("welcome") && body.config.welcomeMessage && channel.type === ChannelType.GuildText) {
            try {
              await (channel as TextChannel).send(body.config.welcomeMessage)
              logInfo(`Sent welcome message to ${channel.name}`)
            } catch (err) {
              logError(`Failed to send welcome message:`, err)
            }
          }
          
          // 如果是规则频道，发送规则
          if (channelConfig.name.toLowerCase().includes("rule") && body.config.rules && body.config.rules.length > 0 && channel.type === ChannelType.GuildText) {
            try {
              const rulesText = "**社区规则**\n\n" + body.config.rules.map((rule, index) => `${index + 1}. ${rule}`).join("\n\n")
              await (channel as TextChannel).send(rulesText)
              logInfo(`Sent rules to ${channel.name}`)
            } catch (err) {
              logError(`Failed to send rules:`, err)
            }
          }
        } catch (err) {
          logError(`Failed to create channel ${channelConfig.name}:`, err)
        }
      }

      return NextResponse.json({
        success: true,
        result: {
          guildId: targetGuild.id,
          guildName: guild.name,
          channelsCreated: createdChannels.filter(c => !c.existing).length,
          channelsSkipped: createdChannels.filter(c => c.existing).length,
          channels: createdChannels
        }
      })
    } catch (fetchError: any) {
      logError(`获取服务器或部署过程中出错`, fetchError);
      return NextResponse.json({ 
        error: "Error during fetch or deployment process", 
        message: fetchError.message,
        stack: process.env.NODE_ENV === 'development' ? fetchError.stack : undefined
      }, { status: 500 })
    }
  } catch (error: any) {
    logError("部署过程中出现错误:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 })
  }
}
