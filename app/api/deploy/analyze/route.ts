import { type NextRequest, NextResponse } from "next/server"
import { Client, GatewayIntentBits, ChannelType, TextChannel, VoiceChannel, NewsChannel, StageChannel, ForumChannel, MediaChannel } from "discord.js"

// 定义类型
interface ChannelConfig {
  name: string;
  type: "text" | "voice" | "announcement";
  position: number;
  isPublic: boolean;
  description: string;
}

interface ServerConfig {
  channels: ChannelConfig[];
  welcomeMessage: string;
  rules: string[];
  categories: string[];
}

interface StructuredChannel {
  id: string;
  name: string;
  type: string;
  position: number;
  isPublic: boolean;
  description: string;
  category: string | null;
}

interface StructuredChannels {
  categories: string[];
  channels: StructuredChannel[];
}

interface SimilarMatch {
  requested: string;
  existing: string;
  similarity: number;
}

interface ChannelAnalysis {
  exactMatches: string[];
  similarMatches: SimilarMatch[];
  newChannels: string[];
  unusedChannels: string[];
  matchRate: number;
}

// 初始化Discord客户端
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
  ],
})

// 确保客户端已登录
let clientReady = false
client.on('ready', () => {
  console.log(`Discord bot logged in as ${client.user?.tag}`)
  clientReady = true
})

// 登录客户端
if (!clientReady) {
  client.login(process.env.DISCORD_BOT_TOKEN).catch(err => {
    console.error("Failed to login to Discord:", err)
  })
}

// 简单的日志工具
const logInfo = (message: string) => {
  console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
};

const logError = (message: string, error?: any) => {
  console.error(`[ERROR] ${new Date().toISOString()}: ${message}`);
  if (error) {
    console.error(error);
  }
};

// 频道类型映射
const channelTypeMap = {
  [ChannelType.GuildText]: "text",
  [ChannelType.GuildVoice]: "voice",
  [ChannelType.GuildAnnouncement]: "announcement",
  [ChannelType.GuildCategory]: "category",
};

export async function POST(request: NextRequest) {
  try {
    const { config } = await request.json() as { config: ServerConfig };
    const accessToken = request.cookies.get("discord_access_token")?.value

    logInfo(`开始分析服务器频道结构`);

    if (!accessToken) {
      logError("未授权访问 - 缺少访问令牌");
      return NextResponse.json({ error: "Not authorized" }, { status: 401 })
    }

    // 确保Discord机器人已登录
    if (!client.isReady()) {
      await new Promise<void>((resolve) => {
        const checkReady = () => {
          if (client.isReady()) {
            resolve();
          } else {
            setTimeout(checkReady, 100);
          }
        };
        checkReady();
      });
    }

    // 获取用户的服务器列表
    logInfo(`正在获取用户的Discord服务器列表...`);
    
    try {
      const guildsResponse = await fetch("https://discord.com/api/users/@me/guilds", {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })

      if (!guildsResponse.ok) {
        const errorData = await guildsResponse.json().catch(() => ({}));
        logError(`获取服务器列表失败: ${guildsResponse.status} ${guildsResponse.statusText}`, errorData);
        return NextResponse.json({ 
          error: "Failed to fetch guilds", 
          details: {
            status: guildsResponse.status,
            statusText: guildsResponse.statusText,
            error: errorData
          }
        }, { status: 400 })
      }

      const guilds = await guildsResponse.json()
      logInfo(`成功获取到${guilds.length}个服务器`);

      // 找到用户有管理权限的服务器
      const targetGuild = guilds.find(
        (guild: any) =>
          (guild.permissions & 0x8) === 0x8 || // Administrator
          (guild.permissions & 0x10) === 0x10, // Manage Channels
      )

      if (!targetGuild) {
        logError(`未找到合适的服务器 - 用户没有管理员权限的服务器`);
        return NextResponse.json({ 
          error: "No suitable guild found", 
          details: {
            message: "User doesn't have administrator permissions in any guild",
          }
        }, { status: 400 })
      }

      logInfo(`选择目标服务器: ${targetGuild.name} (${targetGuild.id})`);

      // 获取目标服务器
      const guild = await client.guilds.fetch(targetGuild.id)
      if (!guild) {
        logError(`找不到目标服务器`);
        return NextResponse.json({ error: "找不到目标服务器" }, { status: 404 })
      }

      // 获取现有频道
      const existingChannels = await guild.channels.fetch()
      
      // 将现有频道结构化
      const structuredChannels: StructuredChannels = {
        categories: [],
        channels: [],
      };
      
      // 先找出所有分类
      existingChannels.forEach(channel => {
        if (channel && channel.type === ChannelType.GuildCategory) {
          structuredChannels.categories.push(channel.name);
        }
      });
      
      // 然后处理所有频道
      existingChannels.forEach(channel => {
        if (channel && channel.type !== ChannelType.GuildCategory) {
          const parent = channel.parent;
          
          // 获取频道描述（根据频道类型处理）
          let description = "";
          if (channel.type === ChannelType.GuildText || 
              channel.type === ChannelType.GuildAnnouncement || 
              channel.type === ChannelType.GuildForum) {
            description = (channel as TextChannel | NewsChannel | ForumChannel).topic || "";
          }
          
          structuredChannels.channels.push({
            id: channel.id,
            name: channel.name,
            type: channelTypeMap[channel.type as keyof typeof channelTypeMap] || "text",
            position: channel.position,
            isPublic: !channel.permissionsLocked,
            description: description,
            category: parent ? parent.name : null
          });
        }
      });
      
      // 分析用户需求与现有频道的匹配度
      const analysis = analyzeChannels(structuredChannels, config);
      
      return NextResponse.json({
        success: true,
        existingStructure: structuredChannels,
        analysis: analysis,
        recommendation: generateRecommendation(structuredChannels, config, analysis)
      });
      
    } catch (fetchError: any) {
      logError(`获取服务器或分析过程中出错`, fetchError);
      return NextResponse.json({ 
        error: "Error during fetch or analysis process", 
        message: fetchError.message,
        stack: process.env.NODE_ENV === 'development' ? fetchError.stack : undefined
      }, { status: 500 })
    }
  } catch (error: any) {
    logError("分析过程中出现错误:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 })
  }
}

// 分析频道匹配度
function analyzeChannels(existing: StructuredChannels, requested: ServerConfig): ChannelAnalysis {
  const existingNames = existing.channels.map(ch => ch.name.toLowerCase());
  const requestedNames = requested.channels.map(ch => ch.name.toLowerCase());
  
  // 找出完全匹配的频道
  const exactMatches = requestedNames.filter(name => existingNames.includes(name));
  
  // 找出相似的频道（名称包含关系）
  const similarMatches: SimilarMatch[] = [];
  for (const requestedName of requestedNames) {
    if (!exactMatches.includes(requestedName)) {
      for (const existingName of existingNames) {
        if (existingName.includes(requestedName) || requestedName.includes(existingName)) {
          similarMatches.push({
            requested: requestedName,
            existing: existingName,
            similarity: calculateSimilarity(requestedName, existingName)
          });
          break;
        }
      }
    }
  }
  
  // 找出需要新建的频道
  const newChannels = requestedNames.filter(name => 
    !exactMatches.includes(name) && 
    !similarMatches.some(match => match.requested === name)
  );
  
  // 找出可能不需要的现有频道
  const unusedChannels = existingNames.filter(name => 
    !exactMatches.includes(name) && 
    !similarMatches.some(match => match.existing === name)
  );
  
  return {
    exactMatches,
    similarMatches,
    newChannels,
    unusedChannels,
    matchRate: exactMatches.length / requestedNames.length
  };
}

// 计算两个字符串的相似度 (简单实现)
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) {
    return 1.0;
  }
  
  // 计算编辑距离的简化版本
  let distance = 0;
  for (let i = 0; i < shorter.length; i++) {
    if (shorter[i] !== longer[i]) {
      distance++;
    }
  }
  distance += longer.length - shorter.length;
  
  return (longer.length - distance) / longer.length;
}

// 生成频道结构建议
function generateRecommendation(existing: StructuredChannels, requested: ServerConfig, analysis: ChannelAnalysis) {
  // 合并分类
  const categories = [...new Set([...existing.categories, ...requested.categories])];
  
  // 准备最终频道列表
  const channels: any[] = [];
  
  // 添加完全匹配的频道（保留现有的）
  for (const name of analysis.exactMatches) {
    const existingChannel = existing.channels.find(ch => ch.name.toLowerCase() === name);
    if (existingChannel) {
      channels.push({
        ...existingChannel,
        action: "keep",
        message: "保留现有频道"
      });
    }
  }
  
  // 处理相似匹配的频道（建议更新）
  for (const match of analysis.similarMatches) {
    const existingChannel = existing.channels.find(ch => ch.name.toLowerCase() === match.existing);
    const requestedChannel = requested.channels.find(ch => ch.name.toLowerCase() === match.requested);
    
    if (existingChannel && requestedChannel) {
      channels.push({
        ...existingChannel,
        requestedName: requestedChannel.name,
        requestedDescription: requestedChannel.description,
        action: "update",
        message: `建议更新: "${existingChannel.name}" → "${requestedChannel.name}"`
      });
    }
  }
  
  // 添加需要新建的频道
  for (const name of analysis.newChannels) {
    const requestedChannel = requested.channels.find(ch => ch.name.toLowerCase() === name);
    if (requestedChannel) {
      channels.push({
        ...requestedChannel,
        action: "create",
        message: `新建频道: "${requestedChannel.name}"`
      });
    }
  }
  
  // 添加未使用但保留的频道
  for (const name of analysis.unusedChannels) {
    const existingChannel = existing.channels.find(ch => ch.name.toLowerCase() === name);
    if (existingChannel) {
      channels.push({
        ...existingChannel,
        action: "unused",
        message: `未使用的现有频道: "${existingChannel.name}"`
      });
    }
  }
  
  return {
    categories,
    channels: channels.sort((a, b) => {
      // 按操作类型排序: keep, update, create, unused
      const actionPriority = { keep: 0, update: 1, create: 2, unused: 3 };
      return actionPriority[a.action as keyof typeof actionPriority] - actionPriority[b.action as keyof typeof actionPriority];
    }),
    welcomeMessage: requested.welcomeMessage,
    rules: requested.rules
  };
} 