import { type NextRequest, NextResponse } from "next/server"

// 简单的日志工具
const logInfo = (message: string) => {
  console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
};

const logError = (message: string, error?: any) => {
  console.error(`[ERROR] ${new Date().toISOString()}: ${message}`);
  if (error) {
    console.error(error);
  }
};

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const code = searchParams.get("code")
  const error = searchParams.get("error")

  if (error) {
    logError(`Discord授权错误: ${error}`);
    return NextResponse.redirect(new URL("/?error=access_denied", request.url))
  }

  if (!code) {
    logError(`缺少授权码`);
    return NextResponse.redirect(new URL("/?error=no_code", request.url))
  }

  logInfo(`开始处理Discord OAuth回调, code=${code?.substring(0, 5)}...`);

  try {
    // Exchange code for access token
    logInfo(`正在将授权码交换为访问令牌...`);
    const tokenResponse = await fetch("https://discord.com/api/oauth2/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: process.env.DISCORD_CLIENT_ID!,
        client_secret: process.env.DISCORD_CLIENT_SECRET!,
        grant_type: "authorization_code",
        code,
        redirect_uri: `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/discord/callback`,
      }),
    })

    const tokenData = await tokenResponse.json()

    if (!tokenResponse.ok) {
      logError(`交换令牌失败: ${JSON.stringify(tokenData)}`);
      throw new Error("Failed to exchange code for token")
    }

    logInfo(`成功获取访问令牌`);

    // 验证我们是否真的能获取用户的服务器
    logInfo(`正在验证获取服务器列表权限...`);
    
    const guildsResponse = await fetch("https://discord.com/api/users/@me/guilds", {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    })

    if (!guildsResponse.ok) {
      const responseText = await guildsResponse.text();
      logError(`验证服务器列表权限失败: ${guildsResponse.status} ${responseText}`);
      // 依然继续，但记录错误
    } else {
      const guilds = await guildsResponse.json()
      logInfo(`成功验证权限，用户有 ${guilds.length} 个服务器`);
    }

    // Store the authorization data (in a real app, you'd save this to a database)
    // 修改重定向URL，添加auth=success和continue_deploy=true参数
    logInfo(`授权成功，正在重定向回应用...`);
    const response = NextResponse.redirect(new URL("/?auth=success&continue_deploy=true", request.url))

    // Set secure cookies with the authorization data
    response.cookies.set("discord_access_token", tokenData.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: tokenData.expires_in,
    })

    return response
  } catch (error: any) {
    logError("Discord OAuth错误:", error);
    return NextResponse.redirect(new URL(`/?error=auth_failed&message=${encodeURIComponent(error.message)}`, request.url))
  }
}
