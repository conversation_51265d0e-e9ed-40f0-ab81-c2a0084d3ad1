"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bo<PERSON>, MessageCircle, Wand2, Settings2, Loader2 } from "lucide-react";

// 导入新的组件
import { AppNavbar } from "@/components/layout/AppNavbar";
import { HelpFooter } from "@/components/layout/HelpFooter";
import { EnvironmentCheck } from "@/components/setup/EnvironmentCheck";
import { StepIndicator } from "@/components/ui/StepIndicator";
import { ChatWizard } from "@/components/wizard/ChatWizard";
import { ConfigurationForm } from "@/components/wizard/ConfigurationForm";
import { ConfigPreview } from "@/components/preview/ConfigPreview";

// 导入类型和Hook
import { DeploymentConfig, GeneratedConfig, StepType } from "@/lib/types";
import { useAppState } from "@/hooks/useAppState";

export default function DiscordDeployerApp() {
  const {
    state,
    setCurrentStep,
    setDeploymentConfig,
    setGenerating,
    setGeneratedConfig,
    setDeploying,
    setDeploymentResult,
    setDeploymentError,
    setAnalyzing,
    setChannelAnalysis,
    toggleRecommendation,
    toggleDebug,
    resetState,
    canProceedToStep,
  } = useAppState();

  const [configMethod, setConfigMethod] = useState<"chat" | "form" | null>(null);

  // 生成Discord配置
  const generateConfig = async (config: DeploymentConfig) => {
    setGenerating(true);
    setDeploymentError(null);

    try {
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ config }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setGeneratedConfig(data.config);
      setCurrentStep("preview");
    } catch (error) {
      console.error("Error generating config:", error);
      setDeploymentError(error instanceof Error ? error.message : "生成配置时发生错误");
    } finally {
      setGenerating(false);
    }
  };

  // 分析现有频道
  const analyzeChannels = async () => {
    if (!state.generatedConfig) return;

    setAnalyzing(true);
    try {
      const response = await fetch("/api/analyze-channels", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ config: state.generatedConfig }),
      });

      if (response.ok) {
        const data = await response.json();
        setChannelAnalysis(data);
      }
    } catch (error) {
      console.error("Error analyzing channels:", error);
    } finally {
      setAnalyzing(false);
    }
  };

  // 部署到Discord
  const deployToDiscord = async () => {
    if (!state.generatedConfig) return;

    setDeploying(true);
    setDeploymentError(null);

    try {
      const response = await fetch("/api/deploy", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config: state.useRecommendation && state.channelAnalysis?.recommendation 
            ? state.channelAnalysis.recommendation 
            : state.generatedConfig,
          userConfig: state.deploymentConfig,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setDeploymentResult(data);
      setCurrentStep("deploy");
    } catch (error) {
      console.error("Error deploying:", error);
      setDeploymentError(error instanceof Error ? error.message : "部署时发生错误");
    } finally {
      setDeploying(false);
    }
  };

  // 处理配置完成
  const handleConfigComplete = (config: DeploymentConfig) => {
    setDeploymentConfig(config);
    generateConfig(config);
  };

  // 渲染介绍步骤
  const renderIntroStep = () => (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center space-y-4">
        <Bot className="h-16 w-16 text-purple-600 mx-auto" />
        <h1 className="text-4xl font-bold text-gray-900">
          AI Discord社区构建器
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          通过AI对话或表单配置，自动生成并部署完美的Discord服务器结构
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => {
          setConfigMethod("chat");
          setCurrentStep("config");
        }}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5 text-purple-600" />
              AI对话配置
            </CardTitle>
            <CardDescription>
              通过与AI助手对话，轻松配置你的Discord社区
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• 智能问答引导</li>
              <li>• 个性化建议</li>
              <li>• 适合新手用户</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => {
          setConfigMethod("form");
          setCurrentStep("config");
        }}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings2 className="h-5 w-5 text-purple-600" />
              表单配置
            </CardTitle>
            <CardDescription>
              使用详细表单快速配置所有参数
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• 完整功能选项</li>
              <li>• 精确控制</li>
              <li>• 适合高级用户</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // 渲染配置步骤
  const renderConfigStep = () => (
    <div className="max-w-4xl mx-auto">
      {configMethod === "chat" ? (
        <ChatWizard onComplete={handleConfigComplete} />
      ) : (
        <ConfigurationForm 
          initialConfig={state.deploymentConfig}
          onSubmit={handleConfigComplete} 
        />
      )}
    </div>
  );

  // 渲染预览步骤
  const renderPreviewStep = () => {
    if (!state.generatedConfig) {
      return (
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>正在生成配置...</p>
        </div>
      );
    }

    return (
      <div className="max-w-4xl mx-auto">
        <ConfigPreview
          config={state.generatedConfig}
          channelAnalysis={state.channelAnalysis}
          useRecommendation={state.useRecommendation}
          onToggleRecommendation={toggleRecommendation}
          onDeploy={deployToDiscord}
          isDeploying={state.isDeploying}
        />
      </div>
    );
  };

  // 渲染部署步骤
  const renderDeployStep = () => (
    <div className="max-w-2xl mx-auto text-center space-y-6">
      <div className="text-green-600">
        <Bot className="h-16 w-16 mx-auto mb-4" />
        <h2 className="text-2xl font-bold">部署成功！</h2>
        <p className="text-gray-600 mt-2">
          你的Discord社区已成功创建并配置完成
        </p>
      </div>

      {state.deploymentResult && (
        <Card>
          <CardHeader>
            <CardTitle>部署结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>服务器: {state.deploymentResult.result?.guildName}</p>
              <p>创建频道: {state.deploymentResult.result?.channelsCreated}</p>
              <p>跳过频道: {state.deploymentResult.result?.channelsSkipped}</p>
            </div>
          </CardContent>
        </Card>
      )}

      <Button onClick={resetState} variant="outline">
        创建新的社区
      </Button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <AppNavbar />
      
      <main className="container mx-auto px-4 py-8">
        <EnvironmentCheck />
        
        <StepIndicator 
          currentStep={state.currentStep} 
          onStepClick={(step) => {
            if (canProceedToStep(step)) {
              setCurrentStep(step);
            }
          }}
        />

        {state.deploymentError && (
          <div className="max-w-4xl mx-auto mb-6">
            <Card className="border-red-300 bg-red-50">
              <CardContent className="pt-6">
                <p className="text-red-700">{state.deploymentError}</p>
              </CardContent>
            </Card>
          </div>
        )}

        {state.currentStep === "intro" && renderIntroStep()}
        {state.currentStep === "config" && renderConfigStep()}
        {state.currentStep === "preview" && renderPreviewStep()}
        {state.currentStep === "deploy" && renderDeployStep()}
      </main>

      <HelpFooter />
    </div>
  );
}
