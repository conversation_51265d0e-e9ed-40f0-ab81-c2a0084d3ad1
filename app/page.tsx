"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Bot, Zap, Shield, Users, Info, HelpCircle, MessageCircle, Loader2, SendIcon, FolderIcon, Hash as HashtagIcon, Volume as VolumeIcon, Megaphone as MegaphoneIcon, AlertCircle } from "lucide-react"
import Link from "next/link"
import { Chat } from './components/chat'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { toast } from "@/components/ui/use-toast"
import { MultiAuth } from './components/multi-auth'

// 定义枚举类型
type StepType = "intro" | "config" | "preview" | "deploy";

// 在文件顶部添加步骤常量
const STEP_INTRO: StepType = "intro";
const STEP_CONFIG: StepType = "config";
const STEP_PREVIEW: StepType = "preview";
const STEP_DEPLOY: StepType = "deploy";

interface DeploymentConfig {
  creatorType: string
  audienceType: string
  contentStyle: string
  features: string[]
  customRequirements: string
}

interface GeneratedConfig {
  channels: Array<{
    name: string
    type: "text" | "voice" | "announcement"
    position: number
    isPublic: boolean
    description: string
  }>
  welcomeMessage: string
  rules: string[]
  categories: string[]
}

// 在文件顶部添加新的状态类型
interface ChannelAnalysisResult {
  success: boolean;
  existingStructure: {
    categories: string[];
    channels: any[];
  };
  analysis: {
    exactMatches: string[];
    similarMatches: any[];
    newChannels: string[];
    unusedChannels: string[];
    matchRate: number;
  };
  recommendation: {
    categories: string[];
    channels: any[];
    welcomeMessage: string;
    rules: string[];
  };
}

// 添加导航组件
function AppNavbar() {
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Bot className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4 text-xl font-medium text-gray-900">AI Discord社区构建器</div>
          </div>
          <nav className="flex space-x-4">
            <Link href="/" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              首页
            </Link>
            <Link href="/help" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center gap-1">
              <HelpCircle className="h-4 w-4" />
              <span>帮助</span>
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}

// 添加底部帮助提示组件
function HelpFooter() {
  const [chatOpen, setChatOpen] = useState(true); // 默认打开聊天
  const [chatError, setChatError] = useState(false);
  
  // 组件加载时自动打开聊天
  useEffect(() => {
    // 延迟1秒后打开聊天，给页面加载留出时间
    const timer = setTimeout(() => {
      setChatOpen(true);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div className="fixed bottom-4 right-4 flex flex-col items-end">
      {chatOpen && (
        <div className="mb-4 w-80 md:w-96">
          <Chat 
            title="AI 助手" 
            description="有任何问题可以直接咨询我"
            onSendMessage={async (message) => {
              try {
                setChatError(false);
                const response = await fetch('/api/chat', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ message }),
                });
                
                if (!response.ok) {
                  console.error(`聊天API错误: ${response.status}`);
                  setChatError(true);
                  throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                return data.response;
              } catch (error) {
                console.error('Error calling chat API:', error);
                setChatError(true);
                return "抱歉，我暂时无法回应您的问题。请稍后再试。";
              }
            }}
          />
        </div>
      )}
      <div className="flex gap-2">
        <Button 
          variant={chatError ? "destructive" : "outline"}
          size="sm" 
          className={`${chatError ? "" : "bg-white shadow-md border-gray-200"} flex items-center gap-2`}
          onClick={() => setChatOpen(!chatOpen)}
        >
          <MessageCircle className="h-4 w-4 text-purple-600" />
          <span>{chatOpen ? "关闭聊天" : "AI助手"}</span>
        </Button>
        <Link href="/help">
          <Button variant="outline" size="sm" className="bg-white shadow-md border-gray-200 flex items-center gap-2">
            <HelpCircle className="h-4 w-4 text-purple-600" />
            <span>帮助中心</span>
          </Button>
        </Link>
      </div>
    </div>
  )
}

// 简化的自定义Select组件
function SimpleSelect({ 
  options, 
  value, 
  onChange, 
  placeholder 
}: { 
  options: string[], 
  value: string, 
  onChange: (value: string) => void, 
  placeholder: string 
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 处理点击外部关闭下拉框
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 过滤选项
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  return (
    <div className="relative" ref={containerRef}>
      <button
        type="button"
        className="flex w-full justify-between rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{value || placeholder}</span>
        <span>▼</span>
      </button>
      
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full overflow-hidden rounded-md border bg-white shadow-lg">
          <div className="p-2 border-b">
            <Input 
              placeholder="搜索..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              className="text-sm"
            />
          </div>
          <div className="max-h-60 overflow-auto py-1">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                  key={option}
                  className={`px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 ${
                    option === value ? 'bg-gray-50 font-medium' : ''
                  }`}
                  onClick={() => {
                    onChange(option);
                    setIsOpen(false);
                    setSearchTerm('');
                  }}
                >
                  {option}
                </div>
              ))
            ) : (
              <div className="px-3 py-2 text-sm text-gray-500">无匹配结果</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// 环境变量检查组件
function EnvironmentCheck() {
  const [missingVars, setMissingVars] = useState<string[]>([]);
  
  useEffect(() => {
    // 检查客户端可用的环境变量
    const checkEnvVariables = async () => {
      try {
        // 使用API端点检查服务器端环境变量
        const response = await fetch('/api/check-env');
        if (response.ok) {
          const data = await response.json();
          setMissingVars(data.missingVars || []);
        } else {
          // 如果API调用失败，仍然检查客户端可用的变量
          checkClientEnvVars();
        }
      } catch (error) {
        console.error("Error checking environment variables:", error);
        // 回退到仅检查客户端变量
        checkClientEnvVars();
      }
    };
    
    const checkClientEnvVars = () => {
      const missing: string[] = [];
      
      // 检查NEXT_PUBLIC前缀的环境变量
      if (!process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID) {
        missing.push('NEXT_PUBLIC_DISCORD_CLIENT_ID');
      }
      
      // 添加用户可能需要设置的其他环境变量的提示
      missing.push('可能需要设置: DISCORD_CLIENT_SECRET, DISCORD_BOT_TOKEN, OPENROUTER_API_KEY');
      
      setMissingVars(missing);
    };
    
    checkEnvVariables();
  }, []);
  
  // 如果没有缺失的变量，不显示任何内容
  if (missingVars.length === 0) {
    return null;
  }
  
  return (
    <Card className="border-red-300 mb-6">
      <CardHeader>
        <CardTitle className="text-red-600">⚠️ 配置缺失</CardTitle>
        <CardDescription>
          应用需要以下环境变量才能正常工作。请参考README.md设置.env.local文件。
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="list-disc pl-6 space-y-1 text-gray-700">
          {missingVars.map((varName, index) => (
            <li key={index}>{varName}</li>
          ))}
        </ul>
        <div className="mt-4 p-3 bg-gray-50 rounded text-sm">
          <p>在项目根目录创建 <code className="bg-gray-200 px-1 rounded">.env.local</code> 文件并添加必要的环境变量。</p>
          <p className="mt-2">注意：现在使用的是OpenRouter的DeepSeek模型，需要OPENROUTER_API_KEY而非OPENAI_API_KEY。</p>
          <p className="mt-2">更多详情请参考 <a href="https://github.com/yourusername/discord-deployer" className="text-blue-600 hover:underline">README.md</a></p>
        </div>
      </CardContent>
    </Card>
  );
}

// 添加一个新的ChatWizard组件，用于引导式对话
function ChatWizard({
  onComplete
}: {
  onComplete: (config: DeploymentConfig) => void
}) {
  const [messages, setMessages] = useState<{role: 'user' | 'assistant' | 'system', content: string}[]>([
    {
      role: 'assistant',
      content: '👋 你好！我是AI助手，将帮助你创建完美的Discord社区。我会问你几个问题来了解你的需求。准备好了吗？'
    }
  ]);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isThinking, setIsThinking] = useState(false);
  const [collectedData, setCollectedData] = useState<Partial<DeploymentConfig>>({
    features: []
  });
  
  const steps = [
    {
      question: "你是什么类型的创作者？例如：YouTuber、音乐人、艺术家、教育工作者等。",
      field: "creatorType"
    },
    {
      question: "你的主要受众是谁？例如：年轻人、专业人士、学生、粉丝等。",
      field: "audienceType"
    },
    {
      question: "描述一下你的内容风格。例如：教育性、娱乐性、激励性等。",
      field: "contentStyle"
    },
    {
      question: "你希望在Discord社区中包含哪些功能？可以多选，例如：欢迎频道、问答区、语音聊天、内容分享等。",
      field: "features"
    },
    {
      question: "有任何特殊需求或想法吗？",
      field: "customRequirements"
    }
  ];
  
  const handleSendMessage = async (userMessage: string) => {
    if (isThinking) return;
    
    // 添加用户消息
    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setIsThinking(true);
    
    // 处理当前步骤的回答
    const currentField = steps[currentStep].field;
    
    // 更新收集的数据
    if (currentField === "features") {
      // 对于features，我们需要解析多个选项
      try {
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            message: `Extract features from this text and return them as a comma-separated list. Text: "${userMessage}"` 
          }),
        });
        
        if (response.ok) {
          const data = await response.json();
          const featuresText = data.response;
          // 分割为数组
          const featuresArray = featuresText.split(/,|、/).map((item: string) => item.trim()).filter(Boolean);
          
          setCollectedData(prev => ({
            ...prev,
            features: featuresArray
          }));
        }
      } catch (error) {
        console.error("Error parsing features:", error);
        // 如果解析失败，直接使用用户输入
        setCollectedData(prev => ({
          ...prev,
          features: [userMessage]
        }));
      }
    } else {
      // 对于其他字段，直接使用用户输入
      setCollectedData(prev => ({
        ...prev,
        [currentField]: userMessage
      }));
    }
    
    // 判断是否进入下一步
    if (currentStep < steps.length - 1) {
      // 还有下一步
      setTimeout(() => {
        // 添加助手回复
        setMessages(prev => [...prev, { 
          role: 'assistant', 
          content: `谢谢！我已记录下你的回答。\n\n${steps[currentStep + 1].question}` 
        }]);
        setCurrentStep(prev => prev + 1);
        setIsThinking(false);
      }, 1000);
    } else {
      // 最后一步，完成收集
      setTimeout(async () => {
        // 添加最终确认消息
        setMessages(prev => [...prev, { 
          role: 'assistant', 
          content: "太棒了！我已收集完所有信息。正在为你生成Discord社区配置..." 
        }]);
        
        // 调用完成回调
        onComplete(collectedData as DeploymentConfig);
        setIsThinking(false);
      }, 1500);
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Discord社区创建向导</CardTitle>
        <CardDescription>
          通过对话方式，我将帮助你创建完美的Discord社区
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[400px] overflow-y-auto flex flex-col space-y-4 p-4">
        {messages.map((msg, index) => (
          <div 
            key={index} 
            className={`flex ${msg.role === 'assistant' ? 'justify-start' : 'justify-end'}`}
          >
            <div 
              className={`max-w-[80%] rounded-lg px-4 py-2 ${
                msg.role === 'assistant' 
                  ? 'bg-gray-100 text-gray-900' 
                  : 'bg-purple-600 text-white'
              }`}
            >
              {msg.content.split('\n').map((line, i) => (
                <p key={i} className={i > 0 ? 'mt-2' : ''}>{line}</p>
              ))}
            </div>
          </div>
        ))}
        {isThinking && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-900 rounded-lg px-4 py-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse"></div>
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <form 
          onSubmit={(e) => {
            e.preventDefault();
            const form = e.target as HTMLFormElement;
            const input = form.elements.namedItem('message') as HTMLInputElement;
            if (input.value.trim()) {
              handleSendMessage(input.value);
              input.value = '';
            }
          }}
          className="flex w-full gap-2"
        >
          <Input 
            name="message" 
            placeholder="输入你的回答..." 
            disabled={isThinking} 
            className="flex-1"
          />
          <Button type="submit" disabled={isThinking}>
            {isThinking ? <Loader2 className="h-4 w-4 animate-spin" /> : <SendIcon className="h-4 w-4" />}
            <span className="sr-only">发送</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}

export default function HomePage() {
  console.log("HomePage组件初始化渲染");
  
  const [currentStep, setCurrentStep] = useState<StepType>(STEP_INTRO)
  const [deploymentConfig, setDeploymentConfig] = useState<DeploymentConfig>({
    creatorType: "",
    audienceType: "",
    contentStyle: "",
    features: [],
    customRequirements: ""
  })
  const [generatedConfig, setGeneratedConfig] = useState<GeneratedConfig | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isDeploying, setIsDeploying] = useState(false)
  const [deploymentResult, setDeploymentResult] = useState<any>(null)
  const [deploymentError, setDeploymentError] = useState<string | null>(null)
  const [channelAnalysis, setChannelAnalysis] = useState<ChannelAnalysisResult | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [useRecommendation, setUseRecommendation] = useState(false)
  const [isDebug, setIsDebug] = useState(false)
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  
  console.log("初始状态：", { currentStep, deploymentConfig, isGenerating, isDeploying });

  // 使用useCallback优化deployConfiguration，减少重复渲染
  const deployConfiguration = useCallback(async () => {
    setIsDeploying(true)

    try {
      // 先检查是否有存储的配置
      let configToUse = generatedConfig;
      if (!configToUse) {
        const savedConfig = localStorage.getItem('discord_builder_generated_config');
        if (savedConfig) {
          configToUse = JSON.parse(savedConfig);
          setGeneratedConfig(configToUse); // 更新状态
        }
      }

      if (!configToUse) {
        throw new Error("没有找到配置信息，请重新生成配置");
      }

      const response = await fetch("/api/deploy", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config: configToUse,
          userConfig: deploymentConfig,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`部署失败: ${errorData.error || '未知错误'}`);
      }

      const result = await response.json();
      // 添加更多日志，便于调试
      console.log("部署成功:", result);
      
      // 保存Discord服务器信息到状态
      setDeploymentResult(result.result);
      setCurrentStep(STEP_DEPLOY)
    } catch (error: any) {
      console.error("Deployment error:", error)
      alert(`部署失败: ${error.message}`)
    } finally {
      setIsDeploying(false)
    }
  }, [deploymentConfig, generatedConfig]);

  // 从LocalStorage加载状态 - 只在初始渲染时执行一次
  useEffect(() => {
    const loadFromLocalStorage = () => {
      try {
        const savedConfig = localStorage.getItem('discord_builder_config');
        const savedGeneratedConfig = localStorage.getItem('discord_builder_generated_config');
        
        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig);
          setDeploymentConfig(parsedConfig);
        }
        
        if (savedGeneratedConfig) {
          const parsedGeneratedConfig = JSON.parse(savedGeneratedConfig);
          setGeneratedConfig(parsedGeneratedConfig);
          
          // 只有在初始状态时设置步骤
          if (currentStep === STEP_INTRO) {
            setCurrentStep(STEP_PREVIEW);
          }
        }
      } catch (error) {
        console.error("Failed to load state from localStorage:", error);
      }
    };

    // 检查URL参数
    const checkUrlParams = () => {
      if (typeof window === 'undefined') return;
      
      const urlParams = new URLSearchParams(window.location.search);
      const continueDeployParam = urlParams.get('continue_deploy');
      const authParam = urlParams.get('auth');
      const debugParam = urlParams.get('debug');
      const errorParam = urlParams.get('error');
      const errorMessage = urlParams.get('message');
      
      if (errorParam) {
        setErrorDetails(`错误类型: ${errorParam}${errorMessage ? `\n错误信息: ${errorMessage}` : ''}`);
      }
      
      if (debugParam === 'true') {
        setIsDebug(true);
      }
      
      if (continueDeployParam === 'true' && authParam === 'success') {
        // 设置标记以触发部署
        localStorage.setItem('should_deploy', 'true');
      }
    };

    loadFromLocalStorage();
    checkUrlParams();
    
    // 在组件销毁时清理localStorage中的临时标记
    return () => {
      localStorage.removeItem('should_deploy');
    };
  }, []); // 空依赖数组确保只在组件挂载时运行一次

  // 当配置或生成的配置变化时，保存到LocalStorage
  // 添加防抖逻辑避免频繁保存
  useEffect(() => {
    const saveTimeout = setTimeout(() => {
      try {
        if (Object.keys(deploymentConfig).length > 0 && deploymentConfig.creatorType) {
          localStorage.setItem('discord_builder_config', JSON.stringify(deploymentConfig));
        }
      } catch (error) {
        console.error("Failed to save config to localStorage:", error);
      }
    }, 500); // 添加500ms延迟

    return () => clearTimeout(saveTimeout); // 清理timeout
  }, [deploymentConfig]);

  useEffect(() => {
    const saveTimeout = setTimeout(() => {
      try {
        if (generatedConfig) {
          localStorage.setItem('discord_builder_generated_config', JSON.stringify(generatedConfig));
        }
      } catch (error) {
        console.error("Failed to save generatedConfig to localStorage:", error);
      }
    }, 500); // 添加500ms延迟

    return () => clearTimeout(saveTimeout); // 清理timeout
  }, [generatedConfig]);
  
  // 处理部署成功后的自动跳转
  useEffect(() => {
    if (currentStep === STEP_DEPLOY && deploymentResult && deploymentResult.guildId) {
      // 清除本地存储的配置，完成后不再需要
      try {
        localStorage.removeItem('discord_builder_config');
        localStorage.removeItem('discord_builder_generated_config');
      } catch (error) {
        console.error("Failed to clear localStorage:", error);
      }
      
      // 设置一个短暂的延迟，让用户看到成功消息
      const timer = setTimeout(() => {
        window.open(`https://discord.com/channels/${deploymentResult.guildId}`, "_blank");
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, [deploymentResult, currentStep]);

  // 单独的useEffect来处理部署
  useEffect(() => {
    const handleDeployment = () => {
      const shouldDeploy = localStorage.getItem('should_deploy');
      if (shouldDeploy === 'true' && generatedConfig) {
        // 清除标记以防止重复触发
        localStorage.removeItem('should_deploy');
        // 延迟执行以确保所有状态已更新
        setTimeout(() => {
          deployConfiguration();
        }, 500);
      }
    };
    
    handleDeployment();
  }, [generatedConfig, deployConfiguration]);

  const creatorTypes = [
    "YouTuber/Content Creator",
    "Instagram Influencer",
    "TikTok Creator",
    "Artist/Designer",
    "Photographer",
    "Fitness Coach",
    "Musician",
    "Educator/Teacher",
    "Writer/Blogger",
    "Podcast Host",
    "Streamer/Gamer",
    "Digital Marketer",
    "NFT/Crypto Creator",
    "Makeup Artist",
    "Fashion Designer",
    "Dancer/Choreographer",
    "Chef/Food Creator",
    "Travel Blogger",
    "Business Coach",
    "Other",
  ]

  const audienceTypes = [
    "Young Adults (18-25)",
    "Adults (25-35)",
    "Professionals",
    "Students",
    "Hobbyists",
    "Fans/Followers",
    "Learning Community",
    "Creative Professionals",
    "Entrepreneurs",
    "Tech Enthusiasts",
    "Wellness Enthusiasts",
    "Gaming Community",
    "Fashion Enthusiasts",
    "Parents",
    "Seniors",
    "International Audience",
    "Industry Experts",
    "Brand Partners",
    "Mixed Audience",
  ]

  const availableFeatures = [
    "Welcome Channel",
    "General Discussion",
    "Q&A Section",
    "Announcements",
    "Voice Chat Rooms",
    "Content Sharing",
    "Feedback/Reviews",
    "Private VIP Area",
    "Events/Activities",
    "Resources/Links",
    "Collaboration Space",
    "Portfolio Showcase",
    "Tutorial/Learning Area",
    "Bot Commands",
    "Music Listening",
    "Member Introductions",
    "Community Challenges",
    "Live Stream Discussions",
    "Mentorship Program",
    "Job Opportunities",
    "Regional/Language Channels",
    "Support Ticket System",
    "Marketplace/Trading",
    "Meme/Fun Zone",
  ]

  // 使用useCallback优化处理函数
  const handleCreatorTypeChange = useCallback((value: string) => {
    setDeploymentConfig((prev) => ({ ...prev, creatorType: value }));
  }, []);

  const handleAudienceTypeChange = useCallback((value: string) => {
    setDeploymentConfig((prev) => ({ ...prev, audienceType: value }));
  }, []);

  const handleContentStyleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setDeploymentConfig((prev) => ({ ...prev, contentStyle: e.target.value }));
  }, []);

  const handleCustomRequirementsChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDeploymentConfig((prev) => ({ ...prev, customRequirements: e.target.value }));
  }, []);

  const handleFeatureToggle = useCallback((feature: string) => {
    setDeploymentConfig((prev) => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter((f) => f !== feature)
        : [...prev.features, feature],
    }));
  }, []);

  // 添加全选/全不选功能
  const handleSelectAllFeatures = useCallback(() => {
    setDeploymentConfig((prev) => ({
      ...prev,
      features: [...availableFeatures],
    }));
  }, []);

  const handleDeselectAllFeatures = useCallback(() => {
    setDeploymentConfig((prev) => ({
      ...prev,
      features: [],
    }));
  }, []);

  // 使用useCallback优化generateConfiguration
  const generateConfiguration = useCallback(async () => {
    setIsGenerating(true);
    setErrorDetails(null);

    try {
      // 获取所有用户选择的功能，包括可能不在availableFeatures中的自定义功能
      const allSelectedFeatures = deploymentConfig.features;
      
      // 调用我们的生成API
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          creatorType: deploymentConfig.creatorType,
          audienceType: deploymentConfig.audienceType,
          contentStyle: deploymentConfig.contentStyle,
          features: allSelectedFeatures,
          customRequirements: deploymentConfig.customRequirements,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`生成配置失败: ${errorData.error || '未知错误'}`);
      }

      const generatedConfig = await response.json();
      
      console.log("生成的配置:", generatedConfig);
      setGeneratedConfig(generatedConfig);
      setCurrentStep(STEP_PREVIEW);
    } catch (error: any) {
      console.error("配置生成错误:", error);
      setErrorDetails(`生成配置时出错: ${error.message}`);
      // 使用默认配置作为备选
      fallbackToDefaultConfig();
    } finally {
      setIsGenerating(false);
    }
  }, [deploymentConfig]);

  // 备选配置生成函数
  const fallbackToDefaultConfig = () => {
    // 这是一个备用配置，当API调用失败时使用
    const mockConfig: GeneratedConfig = {
      channels: [
        { name: "welcome", type: "text", position: 0, isPublic: true, description: "Welcome new members" },
        { name: "announcements", type: "announcement", position: 1, isPublic: true, description: "Important updates" },
        { name: "general-chat", type: "text", position: 2, isPublic: true, description: "General discussion" },
        { name: "content-sharing", type: "text", position: 3, isPublic: true, description: "Share your content" },
        { name: "voice-lounge", type: "voice", position: 4, isPublic: true, description: "Voice chat room" },
        ...(deploymentConfig.features.includes("Private VIP Area")
          ? [
              {
                name: "vip-only",
                type: "text" as const,
                position: 5,
                isPublic: false,
                description: "Exclusive VIP content",
              },
            ]
          : []),
      ],
      welcomeMessage: `🎉 Welcome to our ${deploymentConfig.creatorType} community! 

We're excited to have you join our ${deploymentConfig.audienceType.toLowerCase()} focused community. Here you can connect with like-minded people, share experiences, and grow together.

📋 **Quick Start:**
• Check out <#announcements> for updates
• Introduce yourself in <#general-chat>
• Share your work in <#content-sharing>

Let's build something amazing together! 🚀`,
      rules: [
        "Be respectful and kind to all members",
        "No spam or excessive self-promotion",
        "Keep discussions relevant to the channel topic",
        "No harassment, hate speech, or discrimination",
        "Follow Discord's Terms of Service",
      ],
      categories: ["WELCOME", "COMMUNITY", "CONTENT", "VOICE CHANNELS"],
    };

    setGeneratedConfig(mockConfig);
    setCurrentStep(STEP_PREVIEW);
  };

  // 使用useCallback优化handleDiscordAuth
  const handleDiscordAuth = useCallback(() => {
    // 保存当前状态到localStorage以便重定向后恢复
    try {
      localStorage.setItem('discord_builder_config', JSON.stringify(deploymentConfig));
      if (generatedConfig) {
        localStorage.setItem('discord_builder_generated_config', JSON.stringify(generatedConfig));
      }
    } catch (error) {
      console.error("Failed to save state to localStorage:", error);
    }

    // Redirect to Discord OAuth
    const clientId = process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID
    const redirectUri = encodeURIComponent(`${window.location.origin}/api/auth/discord/callback`)
    // 扩展权限请求，添加管理服务器、管理角色等必要权限
    const scope = "bot%20applications.commands%20guilds%20identify"  // 添加guilds和identify权限
    const permissions = "8" // 使用 8 (Administrator) 而不是 8192，确保有足够权限

    window.location.href = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&permissions=${permissions}&scope=${scope}&redirect_uri=${redirectUri}&response_type=code`
  }, [deploymentConfig, generatedConfig]);

  // 添加调试功能
  const handleDebug = () => {
    // 打开一个新窗口，显示本地存储的内容
    const debugData = {
      localStorage: {
        discord_builder_config: JSON.parse(localStorage.getItem('discord_builder_config') || '{}'),
        discord_builder_generated_config: JSON.parse(localStorage.getItem('discord_builder_generated_config') || '{}'),
      },
      stateData: {
        currentStep,
        deploymentConfig,
        generatedConfig,
        deploymentResult,
      }
    };
    
    const debugWindow = window.open('', '_blank');
    if (debugWindow) {
      debugWindow.document.write(`
        <html>
          <head><title>调试信息</title></head>
          <body>
            <h1>调试信息</h1>
            <pre>${JSON.stringify(debugData, null, 2)}</pre>
          </body>
        </html>
      `);
    }
  }

  // 在代码中替换直接使用的字符串
  const handleBackToConfig = () => {
    setCurrentStep(STEP_CONFIG);
  };
  
  const handleStartConfiguring = () => {
    console.log("开始创建社区按钮被点击");
    setCurrentStep(STEP_CONFIG);
    
    // 确保步骤状态更新后，执行额外的日志记录
    setTimeout(() => {
      console.log("当前步骤状态:", currentStep);
    }, 100);
  };
  
  const handleStartDeployment = () => {
    setCurrentStep(STEP_DEPLOY);
  };

  // 添加日志记录组件状态
  useEffect(() => {
    console.log("当前步骤状态变化:", currentStep);
  }, [currentStep]);

  // 添加频道分析功能
  const analyzeChannels = async () => {
    if (!generatedConfig) return;
    
    setIsAnalyzing(true);
    setChannelAnalysis(null);
    
    try {
      const response = await fetch('/api/deploy/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: generatedConfig
        }),
      });
      
      if (!response.ok) {
        throw new Error(`分析失败: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      setChannelAnalysis(result);
      
      // 如果匹配率较高，默认使用推荐配置
      if (result.analysis.matchRate > 0.5) {
        setUseRecommendation(true);
      }
      
    } catch (error) {
      console.error('频道分析错误:', error);
      toast({
        title: "分析失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 修改生成配置函数，简化逻辑
  const handleGenerateConfig = async () => {
    setIsGenerating(true)
    setGeneratedConfig(null)
    setDeploymentError(null)
    setChannelAnalysis(null)
    
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deploymentConfig),
      })
      
      if (!response.ok) {
        throw new Error(`生成失败: ${response.status} ${response.statusText}`)
      }
      
      const data = await response.json()
      setGeneratedConfig(data)
      setCurrentStep(STEP_PREVIEW)
      
      // 生成配置后自动分析频道
      setTimeout(() => {
        analyzeChannels();
      }, 500);
      
    } catch (error) {
      console.error('Error generating config:', error)
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 部署函数，使用推荐配置
  const handleDeployment = () => {
    if (!generatedConfig) return;
    
    setIsDeploying(true);
    setDeploymentResult(null);
    setDeploymentError(null);
    
    // 确定使用哪个配置
    const configToUse = useRecommendation && channelAnalysis 
      ? channelAnalysis.recommendation
      : generatedConfig;
    
    fetch('/api/deploy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        config: configToUse,
        userConfig: deploymentConfig
      }),
    })
      .then(async (response) => {
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || `部署失败: ${response.status}`);
        }
        
        setDeploymentResult(data.result);
        setCurrentStep(STEP_DEPLOY);
        toast({
          title: "部署成功",
          description: `已创建 ${data.result.channelsCreated} 个频道`,
        });
      })
      .catch((error) => {
        console.error('Error deploying:', error);
        setDeploymentError(error.message);
        toast({
          title: "部署失败",
          description: error.message,
          variant: "destructive"
        });
      })
      .finally(() => {
        setIsDeploying(false);
      });
  };

  // 按类别分组的频道功能
  const featureCategories = [
    {
      name: "基础频道",
      features: [
        "Welcome Channel",
        "General Discussion",
        "Announcements",
        "Q&A Section",
        "Voice Chat Rooms",
      ]
    },
    {
      name: "内容相关",
      features: [
        "Content Sharing",
        "Portfolio Showcase",
        "Feedback/Reviews",
        "Tutorial/Learning Area",
        "Resources/Links",
      ]
    },
    {
      name: "社区互动",
      features: [
        "Member Introductions",
        "Community Challenges",
        "Events/Activities",
        "Live Stream Discussions",
        "Meme/Fun Zone",
      ]
    },
    {
      name: "专业功能",
      features: [
        "Private VIP Area",
        "Collaboration Space",
        "Mentorship Program",
        "Job Opportunities",
        "Marketplace/Trading",
        "Support Ticket System",
        "Bot Commands",
        "Music Listening",
        "Regional/Language Channels",
      ]
    }
  ];

  if (currentStep === STEP_INTRO) {
    console.log("渲染INTRO步骤");
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-4">
        <div className="max-w-4xl mx-auto">
          <EnvironmentCheck />
          
          <div className="text-center mb-8 pt-8">
            <div className="flex justify-center mb-6">
              <div className="bg-purple-100 p-4 rounded-full">
                <Bot className="w-12 h-12 text-purple-600" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">AI Discord社区构建器</h1>
            <p className="text-xl text-gray-600 mb-4 max-w-2xl mx-auto">
              通过对话式引导，几分钟内创建专业的Discord社区。我们的AI分析您的内容风格和受众，构建完美的服务器结构。
            </p>
            <p className="text-md text-gray-500 mb-8 max-w-2xl mx-auto">
              只需回答几个简单问题，AI助手将帮助您配置Discord频道、欢迎消息和社区规则，无需任何技术知识。
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>如何使用</CardTitle>
                <CardDescription>
                  只需简单几步，您就能拥有一个专业的Discord社区
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="flex items-start gap-4">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <span className="text-lg font-bold text-purple-600">1</span>
                    </div>
                    <div>
                      <h3 className="font-medium">与AI助手对话</h3>
                      <p className="text-sm text-gray-600">通过轻松对话，告诉AI您是什么类型的创作者，您的受众是谁</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <span className="text-lg font-bold text-purple-600">2</span>
                    </div>
                    <div>
                      <h3 className="font-medium">预览AI生成的服务器结构</h3>
                      <p className="text-sm text-gray-600">查看频道配置、欢迎消息和社区规则</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <span className="text-lg font-bold text-purple-600">3</span>
                    </div>
                    <div>
                      <h3 className="font-medium">授权并一键部署</h3>
                      <p className="text-sm text-gray-600">授权后，我们会自动创建所有频道并配置您的Discord服务器</p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                  onClick={() => {
                    console.log("按钮点击 - 开始创建我的社区");
                    setCurrentStep(STEP_CONFIG);
                  }}
                >
                  开始创建我的社区
                </Button>
              </CardFooter>
            </Card>
            
            <MultiAuth 
              onSuccess={(provider, userData) => {
                console.log(`使用 ${provider} 登录成功`, userData);
                setCurrentStep(STEP_CONFIG);
              }}
              onError={(error) => {
                console.error("登录错误:", error);
                toast({
                  title: "登录失败",
                  description: error,
                  variant: "destructive"
                });
              }}
            />
          </div>
        </div>
        
        {/* 用于调试的按钮 */}
        {isDebug && (
          <div className="fixed bottom-4 left-4">
            <Button variant="outline" size="sm" onClick={() => console.log("当前状态:", { currentStep, deploymentConfig })}>
              打印状态
            </Button>
          </div>
        )}
      </div>
    );
  }

  if (currentStep === STEP_CONFIG) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-3xl mx-auto">
          <Card className="mt-8">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>配置您的Discord社区</CardTitle>
                  <CardDescription>
                    填写以下信息，AI将为您创建最适合的Discord服务器
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="creator-type" className="text-base font-medium mb-1 block">创作者类型</Label>
                  <p className="text-sm text-gray-500 mb-2">您是什么类型的创作者？</p>
                  <SimpleSelect
                    options={creatorTypes}
                    value={deploymentConfig.creatorType}
                    onChange={handleCreatorTypeChange}
                    placeholder="选择您的创作者类型"
                  />
                </div>

                <div>
                  <Label htmlFor="audience-type" className="text-base font-medium mb-1 block">目标受众</Label>
                  <p className="text-sm text-gray-500 mb-2">您的主要受众群体是？</p>
                  <SimpleSelect
                    options={audienceTypes}
                    value={deploymentConfig.audienceType}
                    onChange={handleAudienceTypeChange}
                    placeholder="选择您的受众类型"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="content-style" className="text-base font-medium mb-1 block">内容风格</Label>
                <p className="text-sm text-gray-500 mb-2">描述您的内容特点和风格（例如：教育性、娱乐性、专业技术等）</p>
                <Input
                  id="content-style"
                  placeholder="简单描述您的内容特点和风格..."
                  value={deploymentConfig.contentStyle}
                  onChange={handleContentStyleChange}
                />
              </div>

              <div>
                <Label className="text-base font-medium mb-1 block">频道功能</Label>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-500">选择您希望在Discord社区中包含的功能</p>
                  <div className="flex gap-2">
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm" 
                      onClick={handleSelectAllFeatures}
                      className="text-xs h-7"
                    >
                      全选
                    </Button>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm" 
                      onClick={handleDeselectAllFeatures}
                      className="text-xs h-7"
                    >
                      取消全选
                    </Button>
                  </div>
                </div>
                
                {featureCategories.map((category) => (
                  <div key={category.name} className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">{category.name}</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {category.features.map((feature) => (
                        <div key={feature} className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50 border border-gray-100">
                          <Checkbox
                            id={`config_${feature}`}
                            checked={deploymentConfig.features.includes(feature)}
                            onCheckedChange={() => handleFeatureToggle(feature)}
                          />
                          <Label htmlFor={`config_${feature}`} className="text-sm cursor-pointer">
                            {feature}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
                
                <p className="text-xs text-gray-500 mt-2">
                  提示: 选择5-10个功能可以获得更好的频道结构
                </p>
              </div>

              <div>
                <Label htmlFor="custom-requirements" className="text-base font-medium mb-1 block">特殊需求</Label>
                <p className="text-sm text-gray-500 mb-2">有任何特别的需求或想法吗？</p>
                <Textarea
                  id="custom-requirements"
                  placeholder="分享您的任何特殊需求或想法..."
                  value={deploymentConfig.customRequirements}
                  onChange={handleCustomRequirementsChange}
                  className="min-h-[100px]"
                />
              </div>

              <div className="pt-4">
                <Button
                  onClick={handleGenerateConfig}
                  disabled={!deploymentConfig.creatorType || !deploymentConfig.audienceType || isGenerating}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      正在生成配置...
                    </>
                  ) : (
                    <>
                      <Zap className="mr-2 h-4 w-4" />
                      生成Discord服务器配置
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (currentStep === STEP_PREVIEW && generatedConfig) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col md:flex-row gap-4 justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">预览配置</h2>
            <p className="text-gray-500">
              请检查AI生成的Discord服务器配置，确认无误后点击部署
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={handleBackToConfig}
              disabled={isDeploying}
            >
              返回编辑
            </Button>
            <Button 
              onClick={analyzeChannels}
              disabled={isAnalyzing || isDeploying}
              variant="secondary"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  分析中...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  {channelAnalysis ? '重新分析' : '分析频道'}
                </>
              )}
            </Button>
            <Button 
              onClick={handleStartDeployment}
              disabled={isDeploying}
            >
              {isDeploying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  部署中...
                </>
              ) : (
                <>
                  <Bot className="mr-2 h-4 w-4" />
                  部署到Discord
                </>
              )}
            </Button>
          </div>
        </div>
        
        {/* 频道分析结果 */}
        {channelAnalysis && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-500" />
                频道分析结果
              </CardTitle>
              <CardDescription>
                我们分析了您的Discord服务器现有频道结构，并提供了智能合并建议
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">匹配情况</h3>
                    <ul className="space-y-1 text-sm">
                      <li>完全匹配: {channelAnalysis.analysis.exactMatches.length} 个频道</li>
                      <li>相似匹配: {channelAnalysis.analysis.similarMatches.length} 个频道</li>
                      <li>需要新建: {channelAnalysis.analysis.newChannels.length} 个频道</li>
                      <li>未使用的现有频道: {channelAnalysis.analysis.unusedChannels.length} 个</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">建议操作</h3>
                    <div className="flex items-center gap-2 mb-2">
                      <Checkbox 
                        id="use-recommendation" 
                        checked={useRecommendation}
                        onCheckedChange={(checked) => setUseRecommendation(checked === true)}
                      />
                      <Label htmlFor="use-recommendation">
                        使用智能推荐配置
                      </Label>
                    </div>
                    <p className="text-sm text-gray-500">
                      启用后将保留现有频道并智能合并新频道，避免重复创建
                    </p>
                  </div>
                </div>
                
                <div className="border rounded-md p-4 bg-gray-50">
                  <h3 className="font-medium mb-2">频道操作明细</h3>
                  <div className="max-h-60 overflow-y-auto">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="text-left p-2">频道名称</th>
                          <th className="text-left p-2">操作</th>
                          <th className="text-left p-2">说明</th>
                        </tr>
                      </thead>
                      <tbody>
                        {channelAnalysis.recommendation.channels.map((channel, idx) => (
                          <tr key={idx} className="border-t">
                            <td className="p-2">{channel.name}</td>
                            <td className="p-2">
                              <span className={`inline-block px-2 py-1 rounded text-xs ${
                                channel.action === 'keep' ? 'bg-green-100 text-green-800' : 
                                channel.action === 'update' ? 'bg-yellow-100 text-yellow-800' :
                                channel.action === 'create' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {
                                  channel.action === 'keep' ? '保留' :
                                  channel.action === 'update' ? '更新' :
                                  channel.action === 'create' ? '新建' :
                                  '未使用'
                                }
                              </span>
                            </td>
                            <td className="p-2 text-gray-600">{channel.message}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        {/* 原有的预览内容 */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>频道结构</CardTitle>
              <CardDescription>AI根据您的需求生成的频道结构</CardDescription>
            </CardHeader>
            <CardContent className="max-h-[400px] overflow-y-auto">
              <div className="space-y-6">
                {generatedConfig.categories.map((category, categoryIndex) => (
                  <div key={categoryIndex} className="space-y-2">
                    <h3 className="font-medium text-gray-900 flex items-center gap-1 py-1 px-2 bg-gray-100 rounded">
                      <FolderIcon className="h-4 w-4 text-gray-600" />
                      {category}
                    </h3>
                    <ul className="space-y-1 pl-2">
                      {generatedConfig.channels
                        .filter((_, index) => index % generatedConfig.categories.length === categoryIndex)
                        .map((channel, channelIndex) => (
                          <li key={channelIndex} className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-md">
                            {channel.type === 'text' ? (
                              <HashtagIcon className="h-4 w-4 text-gray-500" />
                            ) : channel.type === 'voice' ? (
                              <VolumeIcon className="h-4 w-4 text-blue-500" />
                            ) : (
                              <MegaphoneIcon className="h-4 w-4 text-orange-500" />
                            )}
                            <span className="font-medium">{channel.name}</span>
                            <span className="text-xs text-gray-500 ml-1">- {channel.description}</span>
                          </li>
                        ))}
                    </ul>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>欢迎消息</CardTitle>
                <CardDescription>发送到欢迎频道的消息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-md">
                  {generatedConfig.welcomeMessage}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>社区规则</CardTitle>
                <CardDescription>发送到规则频道的内容</CardDescription>
              </CardHeader>
              <CardContent className="max-h-60 overflow-y-auto">
                <ol className="list-decimal pl-5 space-y-2">
                  {generatedConfig.rules.map((rule, index) => (
                    <li key={index}>{rule}</li>
                  ))}
                </ol>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {deploymentError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>部署失败</AlertTitle>
            <AlertDescription>{deploymentError}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  }

  if (currentStep === STEP_DEPLOY) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-2xl mx-auto">
          <Card className="mt-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bot className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle>🎉 部署成功！</CardTitle>
              <CardDescription>您的Discord社区已创建和配置完成</CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                您的Discord服务器现已准备就绪，所有频道、欢迎消息和规则都已按照您的偏好配置完成。
              </p>
              <p className="text-sm text-gray-500">正在自动跳转到您的Discord服务器...</p>
              {isDebug && deploymentResult && (
                <pre className="text-xs text-left bg-gray-100 p-2 rounded overflow-auto max-h-40">
                  {JSON.stringify(deploymentResult, null, 2)}
                </pre>
              )}
              <div className="space-y-2">
                <Button className="w-full" onClick={() => window.open(`https://discord.com/channels/${deploymentResult?.guildId || ''}`, "_blank")}>
                  打开Discord服务器
                </Button>
                <Button variant="outline" className="w-full" onClick={() => setCurrentStep(STEP_INTRO)}>
                  创建另一个社区
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div>
      <AppNavbar />
      
      <EnvironmentCheck />
      
      {currentStep === STEP_INTRO && (
        <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8 pt-8">
              <div className="flex justify-center mb-6">
                <div className="bg-purple-100 p-4 rounded-full">
                  <Bot className="w-12 h-12 text-purple-600" />
                </div>
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">AI Discord社区构建器</h1>
              <p className="text-xl text-gray-600 mb-4 max-w-2xl mx-auto">
                通过对话式引导，几分钟内创建专业的Discord社区。我们的AI分析您的内容风格和受众，构建完美的服务器结构。
              </p>
              <p className="text-md text-gray-500 mb-8 max-w-2xl mx-auto">
                只需回答几个简单问题，AI助手将帮助您配置Discord频道、欢迎消息和社区规则，无需任何技术知识。
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle>如何使用</CardTitle>
                  <CardDescription>
                    只需简单几步，您就能拥有一个专业的Discord社区
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    <div className="flex items-start gap-4">
                      <div className="bg-purple-100 p-2 rounded-full">
                        <span className="text-lg font-bold text-purple-600">1</span>
                      </div>
                      <div>
                        <h3 className="font-medium">与AI助手对话</h3>
                        <p className="text-sm text-gray-600">通过轻松对话，告诉AI您是什么类型的创作者，您的受众是谁</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="bg-purple-100 p-2 rounded-full">
                        <span className="text-lg font-bold text-purple-600">2</span>
                      </div>
                      <div>
                        <h3 className="font-medium">预览AI生成的服务器结构</h3>
                        <p className="text-sm text-gray-600">查看频道配置、欢迎消息和社区规则</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="bg-purple-100 p-2 rounded-full">
                        <span className="text-lg font-bold text-purple-600">3</span>
                      </div>
                      <div>
                        <h3 className="font-medium">授权并一键部署</h3>
                        <p className="text-sm text-gray-600">授权后，我们会自动创建所有频道并配置您的Discord服务器</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                    onClick={() => {
                      console.log("按钮点击 - 开始创建我的社区");
                      setCurrentStep(STEP_CONFIG);
                    }}
                  >
                    开始创建我的社区
                  </Button>
                </CardFooter>
              </Card>
              
              <MultiAuth 
                onSuccess={(provider, userData) => {
                  console.log(`使用 ${provider} 登录成功`, userData);
                  setCurrentStep(STEP_CONFIG);
                }}
                onError={(error) => {
                  console.error("登录错误:", error);
                  toast({
                    title: "登录失败",
                    description: error,
                    variant: "destructive"
                  });
                }}
              />
            </div>
          </div>
          
          {/* 用于调试的按钮 */}
          {isDebug && (
            <div className="fixed bottom-4 left-4">
              <Button variant="outline" size="sm" onClick={() => console.log("当前状态:", { currentStep, deploymentConfig })}>
                打印状态
              </Button>
            </div>
          )}
        </div>
      )}
      
      {currentStep === STEP_CONFIG && (
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-3xl mx-auto">
            <Card className="mt-8">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>配置您的Discord社区</CardTitle>
                    <CardDescription>
                      填写以下信息，AI将为您创建最适合的Discord服务器
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="creator-type" className="text-base font-medium mb-1 block">创作者类型</Label>
                    <p className="text-sm text-gray-500 mb-2">您是什么类型的创作者？</p>
                    <SimpleSelect
                      options={creatorTypes}
                      value={deploymentConfig.creatorType}
                      onChange={handleCreatorTypeChange}
                      placeholder="选择您的创作者类型"
                    />
                  </div>

                  <div>
                    <Label htmlFor="audience-type" className="text-base font-medium mb-1 block">目标受众</Label>
                    <p className="text-sm text-gray-500 mb-2">您的主要受众群体是？</p>
                    <SimpleSelect
                      options={audienceTypes}
                      value={deploymentConfig.audienceType}
                      onChange={handleAudienceTypeChange}
                      placeholder="选择您的受众类型"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="content-style" className="text-base font-medium mb-1 block">内容风格</Label>
                  <p className="text-sm text-gray-500 mb-2">描述您的内容特点和风格（例如：教育性、娱乐性、专业技术等）</p>
                  <Input
                    id="content-style"
                    placeholder="简单描述您的内容特点和风格..."
                    value={deploymentConfig.contentStyle}
                    onChange={handleContentStyleChange}
                  />
                </div>

                <div>
                  <Label className="text-base font-medium mb-1 block">频道功能</Label>
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-sm text-gray-500">选择您希望在Discord社区中包含的功能</p>
                    <div className="flex gap-2">
                      <Button 
                        type="button" 
                        variant="ghost" 
                        size="sm" 
                        onClick={handleSelectAllFeatures}
                        className="text-xs h-7"
                      >
                        全选
                      </Button>
                      <Button 
                        type="button" 
                        variant="ghost" 
                        size="sm" 
                        onClick={handleDeselectAllFeatures}
                        className="text-xs h-7"
                      >
                        取消全选
                      </Button>
                    </div>
                  </div>
                  
                  {featureCategories.map((category) => (
                    <div key={category.name} className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">{category.name}</h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                        {category.features.map((feature) => (
                          <div key={feature} className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50 border border-gray-100">
                            <Checkbox
                              id={`config_${feature}`}
                              checked={deploymentConfig.features.includes(feature)}
                              onCheckedChange={() => handleFeatureToggle(feature)}
                            />
                            <Label htmlFor={`config_${feature}`} className="text-sm cursor-pointer">
                              {feature}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                  
                  <p className="text-xs text-gray-500 mt-2">
                    提示: 选择5-10个功能可以获得更好的频道结构
                  </p>
                </div>

                <div>
                  <Label htmlFor="custom-requirements" className="text-base font-medium mb-1 block">特殊需求</Label>
                  <p className="text-sm text-gray-500 mb-2">有任何特别的需求或想法吗？</p>
                  <Textarea
                    id="custom-requirements"
                    placeholder="分享您的任何特殊需求或想法..."
                    value={deploymentConfig.customRequirements}
                    onChange={handleCustomRequirementsChange}
                    className="min-h-[100px]"
                  />
                </div>

                <div className="pt-4">
                  <Button
                    onClick={handleGenerateConfig}
                    disabled={!deploymentConfig.creatorType || !deploymentConfig.audienceType || isGenerating}
                    className="w-full"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        正在生成配置...
                      </>
                    ) : (
                      <>
                        <Zap className="mr-2 h-4 w-4" />
                        生成Discord服务器配置
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
      
      {currentStep === STEP_PREVIEW && generatedConfig && (
        <div className="space-y-8">
          <div className="flex flex-col md:flex-row gap-4 justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">预览配置</h2>
              <p className="text-gray-500">
                请检查AI生成的Discord服务器配置，确认无误后点击部署
              </p>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={handleBackToConfig}
                disabled={isDeploying}
              >
                返回编辑
              </Button>
              <Button 
                onClick={analyzeChannels}
                disabled={isAnalyzing || isDeploying}
                variant="secondary"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    {channelAnalysis ? '重新分析' : '分析频道'}
                  </>
                )}
              </Button>
              <Button 
                onClick={handleStartDeployment}
                disabled={isDeploying}
              >
                {isDeploying ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    部署中...
                  </>
                ) : (
                  <>
                    <Bot className="mr-2 h-4 w-4" />
                    部署到Discord
                  </>
                )}
              </Button>
            </div>
          </div>
          
          {/* 频道分析结果 */}
          {channelAnalysis && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-purple-500" />
                  频道分析结果
                </CardTitle>
                <CardDescription>
                  我们分析了您的Discord服务器现有频道结构，并提供了智能合并建议
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-medium mb-2">匹配情况</h3>
                      <ul className="space-y-1 text-sm">
                        <li>完全匹配: {channelAnalysis.analysis.exactMatches.length} 个频道</li>
                        <li>相似匹配: {channelAnalysis.analysis.similarMatches.length} 个频道</li>
                        <li>需要新建: {channelAnalysis.analysis.newChannels.length} 个频道</li>
                        <li>未使用的现有频道: {channelAnalysis.analysis.unusedChannels.length} 个</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-medium mb-2">建议操作</h3>
                      <div className="flex items-center gap-2 mb-2">
                        <Checkbox 
                          id="use-recommendation" 
                          checked={useRecommendation}
                          onCheckedChange={(checked) => setUseRecommendation(checked === true)}
                        />
                        <Label htmlFor="use-recommendation">
                          使用智能推荐配置
                        </Label>
                      </div>
                      <p className="text-sm text-gray-500">
                        启用后将保留现有频道并智能合并新频道，避免重复创建
                      </p>
                    </div>
                  </div>
                  
                  <div className="border rounded-md p-4 bg-gray-50">
                    <h3 className="font-medium mb-2">频道操作明细</h3>
                    <div className="max-h-60 overflow-y-auto">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="text-left p-2">频道名称</th>
                            <th className="text-left p-2">操作</th>
                            <th className="text-left p-2">说明</th>
                          </tr>
                        </thead>
                        <tbody>
                          {channelAnalysis.recommendation.channels.map((channel, idx) => (
                            <tr key={idx} className="border-t">
                              <td className="p-2">{channel.name}</td>
                              <td className="p-2">
                                <span className={`inline-block px-2 py-1 rounded text-xs ${
                                  channel.action === 'keep' ? 'bg-green-100 text-green-800' : 
                                  channel.action === 'update' ? 'bg-yellow-100 text-yellow-800' :
                                  channel.action === 'create' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {
                                    channel.action === 'keep' ? '保留' :
                                    channel.action === 'update' ? '更新' :
                                    channel.action === 'create' ? '新建' :
                                    '未使用'
                                  }
                                </span>
                              </td>
                              <td className="p-2 text-gray-600">{channel.message}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* 原有的预览内容 */}
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>频道结构</CardTitle>
                <CardDescription>AI根据您的需求生成的频道结构</CardDescription>
              </CardHeader>
              <CardContent className="max-h-[400px] overflow-y-auto">
                <div className="space-y-6">
                  {generatedConfig.categories.map((category, categoryIndex) => (
                    <div key={categoryIndex} className="space-y-2">
                      <h3 className="font-medium text-gray-900 flex items-center gap-1 py-1 px-2 bg-gray-100 rounded">
                        <FolderIcon className="h-4 w-4 text-gray-600" />
                        {category}
                      </h3>
                      <ul className="space-y-1 pl-2">
                        {generatedConfig.channels
                          .filter((_, index) => index % generatedConfig.categories.length === categoryIndex)
                          .map((channel, channelIndex) => (
                            <li key={channelIndex} className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-md">
                              {channel.type === 'text' ? (
                                <HashtagIcon className="h-4 w-4 text-gray-500" />
                              ) : channel.type === 'voice' ? (
                                <VolumeIcon className="h-4 w-4 text-blue-500" />
                              ) : (
                                <MegaphoneIcon className="h-4 w-4 text-orange-500" />
                              )}
                              <span className="font-medium">{channel.name}</span>
                              <span className="text-xs text-gray-500 ml-1">- {channel.description}</span>
                            </li>
                          ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>欢迎消息</CardTitle>
                  <CardDescription>发送到欢迎频道的消息</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-4 rounded-md">
                    {generatedConfig.welcomeMessage}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>社区规则</CardTitle>
                  <CardDescription>发送到规则频道的内容</CardDescription>
                </CardHeader>
                <CardContent className="max-h-60 overflow-y-auto">
                  <ol className="list-decimal pl-5 space-y-2">
                    {generatedConfig.rules.map((rule, index) => (
                      <li key={index}>{rule}</li>
                    ))}
                  </ol>
                </CardContent>
              </Card>
            </div>
          </div>
          
          {deploymentError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>部署失败</AlertTitle>
              <AlertDescription>{deploymentError}</AlertDescription>
            </Alert>
          )}
        </div>
      )}
      
      {currentStep !== STEP_INTRO && <HelpFooter />}
    </div>
  )
}
