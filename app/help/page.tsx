"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Chat } from '@/app/components/chat'
import { AppNavbar } from "@/components/layout/AppNavbar"
import { HelpCircle, MessageSquare, FileQuestion, Settings, Bot, Shield, Zap, ExternalLink } from 'lucide-react'
import Link from "next/link"

const faqItems = [
  {
    question: "如何删除已创建的频道？",
    answer: "目前，您可以在Discord服务器设置中手动删除已创建的频道。在未来的更新中，我们将添加直接从该平台删除频道的功能。要手动删除频道，请打开Discord，右键点击频道名称，然后选择"删除频道"选项。"
  },
  {
    question: "我需要哪些Discord权限才能使用此工具？",
    answer: "您需要是Discord服务器的管理员或拥有者，具有"管理服务器"、"管理频道"和"管理角色"的权限。在授权过程中，我们会自动检查这些权限并告知您是否缺少任何必要权限。"
  },
  {
    question: "我可以自定义生成的频道名称和描述吗？",
    answer: "是的，在部署前的预览阶段，您可以查看所有生成的频道配置。在未来的更新中，我们将添加直接编辑预览中的频道名称和描述的功能。目前，您可以在部署后通过Discord直接编辑这些内容。"
  },
  {
    question: "如果部署过程中出现错误怎么办？",
    answer: "如果在部署过程中发生错误，系统会显示具体的错误信息。常见问题包括权限不足或Discord API限制。您可以根据错误提示解决问题，或者使用聊天助手获取更详细的帮助和解决方案。"
  },
  {
    question: "我可以撤销部署操作吗？",
    answer: "目前，部署操作无法自动撤销。如果您想恢复更改，需要手动删除已创建的频道。在未来的更新中，我们计划添加撤销功能，允许您一键恢复到部署前的状态。"
  },
  {
    question: "AI生成的内容质量不满意怎么办？",
    answer: "如果您对AI生成的内容不满意，可以在预览阶段返回修改您的输入信息，提供更具体的描述和要求。部署后，您也可以随时通过Discord编辑任何内容。我们不断改进我们的AI模型，以提供更好的结果。"
  }
]

export default function HelpPage() {
  // 这里可以添加实际API调用来获取AI回复
  const handleSendMessage = async (message: string): Promise<string> => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Error calling chat API:', error);
      return "抱歉，我暂时无法回应您的问题。请稍后再试。";
    }
  }
  
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">帮助与支持中心</h1>
          <p className="text-gray-600">获取使用AI Discord社区构建器的帮助和指导</p>
        </div>
        
        <Tabs defaultValue="chat" className="mb-8">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span>AI助手聊天</span>
            </TabsTrigger>
            <TabsTrigger value="faq" className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4" />
              <span>常见问题</span>
            </TabsTrigger>
            <TabsTrigger value="guides" className="flex items-center gap-2">
              <FileQuestion className="h-4 w-4" />
              <span>使用指南</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="chat" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>智能AI助手</CardTitle>
                <CardDescription>
                  我们的AI助手可以回答您关于Discord服务器配置和平台使用的问题
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Chat onSendMessage={handleSendMessage} />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="faq" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>常见问题解答</CardTitle>
                <CardDescription>
                  关于平台使用的常见问题和解答
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {faqItems.map((item, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                      <AccordionTrigger className="text-left font-medium">
                        {item.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-gray-600">{item.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="guides" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>使用指南</CardTitle>
                <CardDescription>
                  详细的步骤说明和最佳实践
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">开始使用</h3>
                    <ol className="list-decimal pl-6 space-y-2 text-gray-600">
                      <li>在首页填写您的创作者类型、受众和内容风格</li>
                      <li>选择您需要的社区功能和任何特殊需求</li>
                      <li>点击"生成我的Discord服务器配置"按钮</li>
                      <li>在预览页面查看AI生成的频道结构、欢迎消息和规则</li>
                      <li>如果满意，点击"授权Discord并部署"按钮</li>
                      <li>选择您想配置的Discord服务器并授权应用</li>
                      <li>等待部署完成，系统会自动创建所有频道和设置</li>
                    </ol>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">权限要求</h3>
                    <p className="text-gray-600 mb-2">
                      要使用本工具，您需要是Discord服务器的管理员或所有者，并具有以下权限：
                    </p>
                    <ul className="list-disc pl-6 space-y-1 text-gray-600">
                      <li>管理服务器</li>
                      <li>管理频道</li>
                      <li>管理角色</li>
                      <li>管理消息</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">部署后的管理</h3>
                    <p className="text-gray-600 mb-2">
                      部署完成后，您可以通过Discord直接管理您的服务器：
                    </p>
                    <ul className="list-disc pl-6 space-y-1 text-gray-600">
                      <li>编辑频道名称和描述</li>
                      <li>调整频道顺序和分类</li>
                      <li>修改欢迎消息和社区规则</li>
                      <li>管理权限和角色设置</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 