"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Bot, Mail, AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Image from "next/image"

interface MultiAuthProps {
  onSuccess?: (provider: string, userData: any) => void
  onError?: (error: string) => void
}

export function MultiAuth({ onSuccess, onError }: MultiAuthProps) {
  const [activeTab, setActiveTab] = useState("discord")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 处理Discord登录
  const handleDiscordAuth = () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const clientId = process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID
      const redirectUri = encodeURIComponent(`${window.location.origin}/api/auth/discord/callback`)
      const scope = encodeURIComponent("identify guilds")
      
      // 重定向到Discord授权页面
      window.location.href = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&permissions=8&scope=${scope}&redirect_uri=${redirectUri}&response_type=code`
    } catch (error) {
      setError("Discord授权过程中出错")
      if (onError) onError("Discord授权过程中出错")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理Google登录
  const handleGoogleAuth = () => {
    setIsLoading(true)
    setError(null)
    
    // 这里是示例实现，实际需要配置Google OAuth
    setTimeout(() => {
      setError("Google登录功能正在开发中")
      setIsLoading(false)
      if (onError) onError("Google登录功能正在开发中")
    }, 1000)
  }

  // 处理Instagram登录
  const handleInstagramAuth = () => {
    setIsLoading(true)
    setError(null)
    
    // 这里是示例实现，实际需要配置Instagram OAuth
    setTimeout(() => {
      setError("Instagram登录功能正在开发中")
      setIsLoading(false)
      if (onError) onError("Instagram登录功能正在开发中")
    }, 1000)
  }

  // 处理Telegram登录
  const handleTelegramAuth = () => {
    setIsLoading(true)
    setError(null)
    
    // 这里是示例实现，实际需要配置Telegram OAuth
    setTimeout(() => {
      setError("Telegram登录功能正在开发中")
      setIsLoading(false)
      if (onError) onError("Telegram登录功能正在开发中")
    }, 1000)
  }

  // 处理邮箱登录
  const handleEmailLogin = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    
    // 这里是示例实现，实际需要连接到后端进行验证
    setTimeout(() => {
      if (email && password) {
        if (onSuccess) onSuccess("email", { email })
        setIsLoading(false)
      } else {
        setError("请输入邮箱和密码")
        setIsLoading(false)
        if (onError) onError("请输入邮箱和密码")
      }
    }, 1000)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>登录您的账户</CardTitle>
        <CardDescription>
          选择您偏好的登录方式连接到Discord社区构建器
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="discord" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 mb-4">
            <TabsTrigger value="discord">Discord</TabsTrigger>
            <TabsTrigger value="google">Google</TabsTrigger>
            <TabsTrigger value="instagram">Instagram</TabsTrigger>
            <TabsTrigger value="telegram">Telegram</TabsTrigger>
            <TabsTrigger value="email">邮箱</TabsTrigger>
          </TabsList>
          
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>登录失败</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <TabsContent value="discord">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center">
                <Bot className="w-8 h-8 text-indigo-600" />
              </div>
              <div className="text-center">
                <h3 className="font-medium">使用Discord账户登录</h3>
                <p className="text-sm text-gray-500 mt-1">
                  连接您的Discord账户以管理您的社区
                </p>
              </div>
              <Button 
                className="w-full bg-indigo-600 hover:bg-indigo-700" 
                onClick={handleDiscordAuth}
                disabled={isLoading}
              >
                {isLoading ? "连接中..." : "连接Discord"}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="google">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 rounded-full flex items-center justify-center">
                <Image 
                  src="/google-logo.png" 
                  alt="Google Logo" 
                  width={40} 
                  height={40}
                  className="rounded-full"
                />
              </div>
              <div className="text-center">
                <h3 className="font-medium">使用Google账户登录</h3>
                <p className="text-sm text-gray-500 mt-1">
                  使用您的Google账户快速登录
                </p>
              </div>
              <Button 
                variant="outline"
                className="w-full" 
                onClick={handleGoogleAuth}
                disabled={isLoading}
              >
                {isLoading ? "连接中..." : "连接Google"}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="instagram">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 rounded-full flex items-center justify-center">
                <Image 
                  src="/instagram-logo.png" 
                  alt="Instagram Logo" 
                  width={40} 
                  height={40}
                  className="rounded-full"
                />
              </div>
              <div className="text-center">
                <h3 className="font-medium">使用Instagram账户登录</h3>
                <p className="text-sm text-gray-500 mt-1">
                  连接您的Instagram账户
                </p>
              </div>
              <Button 
                variant="outline"
                className="w-full" 
                onClick={handleInstagramAuth}
                disabled={isLoading}
              >
                {isLoading ? "连接中..." : "连接Instagram"}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="telegram">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <Image 
                  src="/telegram-logo.png" 
                  alt="Telegram Logo" 
                  width={40} 
                  height={40}
                  className="rounded-full"
                />
              </div>
              <div className="text-center">
                <h3 className="font-medium">使用Telegram账户登录</h3>
                <p className="text-sm text-gray-500 mt-1">
                  通过Telegram快速登录
                </p>
              </div>
              <Button 
                variant="outline"
                className="w-full" 
                onClick={handleTelegramAuth}
                disabled={isLoading}
              >
                {isLoading ? "连接中..." : "连接Telegram"}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="email">
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <Input 
                  id="password" 
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <Button 
                type="submit" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "登录中..." : "登录"}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex flex-col">
        <p className="text-xs text-center text-gray-500">
          登录即表示您同意我们的服务条款和隐私政策
        </p>
      </CardFooter>
    </Card>
  )
} 