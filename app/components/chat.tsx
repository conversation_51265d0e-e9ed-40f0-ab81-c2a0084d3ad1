"use client"

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SendIcon, Loader2, Bo<PERSON>, User } from "lucide-react"
import { cn } from "@/lib/utils"

type Message = {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

type QuickQuestion = {
  id: string
  text: string
}

type ChatProps = {
  title?: string
  description?: string
  onSendMessage?: (message: string) => Promise<string>
  initialMessages?: Message[]
  quickQuestions?: QuickQuestion[]
}

export function Chat({
  title = "AI助手",
  description = "您可以询问有关Discord服务器创建和管理的任何问题",
  onSendMessage,
  initialMessages = [],
  quickQuestions = [
    { id: '1', text: "如何为我的社区选择最佳频道结构？" },
    { id: '2', text: "我需要什么权限来创建服务器？" },
    { id: '3', text: "如何自定义欢迎消息？" },
    { id: '4', text: "如何删除已创建的频道？" },
  ]
}: ChatProps) {
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const generateId = () => {
    return Math.random().toString(36).substring(2, 11)
  }

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content: input,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      if (onSendMessage) {
        const response = await onSendMessage(input)
        
        const assistantMessage: Message = {
          id: generateId(),
          role: 'assistant',
          content: response,
          timestamp: new Date()
        }
        
        setMessages(prev => [...prev, assistantMessage])
      } else {
        // 默认响应
        setTimeout(() => {
          const defaultResponse: Message = {
            id: generateId(),
            role: 'assistant',
            content: "我理解您的问题。这是一个示例响应，实际集成时会连接到真实的AI服务。",
            timestamp: new Date()
          }
          setMessages(prev => [...prev, defaultResponse])
        }, 1000)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: "抱歉，处理您的请求时出现了错误。请稍后再试。",
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleQuickQuestion = (text: string) => {
    setInput(text)
  }

  return (
    <Card className="w-full h-full max-h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow overflow-y-auto">
        <div className="space-y-4 pt-1">
          {messages.length === 0 && (
            <div className="flex flex-col items-center justify-center h-full p-8 text-center">
              <Bot className="h-12 w-12 text-purple-500 mb-4" />
              <h3 className="font-medium text-lg">您好！我是您的AI助手</h3>
              <p className="text-gray-500 mt-1 mb-6">您可以询问有关Discord服务器的任何问题</p>
              {quickQuestions.length > 0 && (
                <div className="grid grid-cols-1 gap-2 w-full">
                  {quickQuestions.map((q) => (
                    <Button 
                      key={q.id} 
                      variant="outline" 
                      className="justify-start text-left h-auto py-2 px-3"
                      onClick={() => handleQuickQuestion(q.text)}
                    >
                      {q.text}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          )}
          
          {messages.map((message) => (
            <div 
              key={message.id} 
              className={cn(
                "flex items-start gap-3 pb-2",
                message.role === 'assistant' ? "flex-row" : "flex-row-reverse"
              )}
            >
              <div className={cn(
                "rounded-full p-2 w-8 h-8 flex items-center justify-center",
                message.role === 'assistant' 
                  ? "bg-purple-100" 
                  : "bg-blue-100"
              )}>
                {message.role === 'assistant' 
                  ? <Bot className="h-4 w-4 text-purple-600" /> 
                  : <User className="h-4 w-4 text-blue-600" />}
              </div>
              <div 
                className={cn(
                  "rounded-lg px-3 py-2 max-w-[85%]", 
                  message.role === 'assistant' 
                    ? "bg-gray-100 text-gray-800" 
                    : "bg-blue-500 text-white"
                )}
              >
                <p className="text-sm">{message.content}</p>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex items-start gap-3">
              <div className="rounded-full p-2 w-8 h-8 flex items-center justify-center bg-purple-100">
                <Bot className="h-4 w-4 text-purple-600" />
              </div>
              <div className="bg-gray-100 text-gray-800 rounded-lg px-3 py-2">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">AI助手正在回复...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </CardContent>
      <CardFooter className="pt-0">
        <form 
          onSubmit={(e) => {
            e.preventDefault()
            handleSendMessage()
          }}
          className="flex w-full gap-2"
        >
          <Input
            placeholder="输入问题或选择上方的问题示例..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <SendIcon className="h-4 w-4" />
            )}
            <span className="sr-only">发送</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  )
} 