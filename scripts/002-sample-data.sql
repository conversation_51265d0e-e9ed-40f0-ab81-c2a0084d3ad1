-- Sample data for testing the Discord Community Builder
-- This script adds sample deployments and configurations for development/testing

-- Insert sample guilds
INSERT INTO guilds (discord_guild_id, name, icon, owner_id, permissions) VALUES
('111111111111111111', 'Test Creator Community', 'guild_icon_1', '123456789012345678', 8),
('222222222222222222', 'Fitness Motivation Hub', 'guild_icon_2', '987654321098765432', 8),
('333333333333333333', 'Art & Design Collective', 'guild_icon_3', '123456789012345678', 8)
ON CONFLICT (discord_guild_id) DO NOTHING;

-- Link users to their guilds
INSERT INTO user_guilds (user_id, guild_id, permissions, is_owner) VALUES
(1, 1, 8, TRUE),  -- <PERSON><PERSON><PERSON> owns Test Creator Community
(2, 2, 8, TRUE),  -- <PERSON><PERSON><PERSON> owns Fitness Motivation Hub
(1, 3, 8, TRUE)   -- <PERSON><PERSON><PERSON> owns Art & Design Collective
ON CONFLICT (user_id, guild_id) DO NOTHING;

-- Sample deployment configurations
INSERT INTO deployments (
    user_id, 
    guild_id, 
    creator_type, 
    audience_type, 
    content_style, 
    features, 
    custom_requirements,
    generated_config,
    deployment_status,
    deployed_at
) VALUES
(
    1, 
    1, 
    'YouTuber/Content Creator', 
    'Young Adults (18-25)', 
    'Tech reviews and tutorials',
    '["Welcome Channel", "General Discussion", "Q&A Section", "Content Sharing", "Voice Chat Rooms"]',
    'Need a place for tech discussions and video feedback',
    '{
        "channels": [
            {"name": "welcome", "type": "text", "position": 0, "isPublic": true, "description": "Welcome new members"},
            {"name": "announcements", "type": "announcement", "position": 1, "isPublic": true, "description": "Important updates"},
            {"name": "general-chat", "type": "text", "position": 2, "isPublic": true, "description": "General discussion"},
            {"name": "tech-discussions", "type": "text", "position": 3, "isPublic": true, "description": "Tech talk and reviews"},
            {"name": "video-feedback", "type": "text", "position": 4, "isPublic": true, "description": "Feedback on videos"},
            {"name": "voice-lounge", "type": "voice", "position": 5, "isPublic": true, "description": "Voice chat room"}
        ],
        "welcomeMessage": "🎉 Welcome to our tech community! Share your passion for technology, get help with tech questions, and connect with fellow enthusiasts.",
        "rules": [
            "Be respectful and kind to all members",
            "Keep discussions tech-related in tech channels",
            "No spam or excessive self-promotion",
            "Help others when you can"
        ],
        "categories": ["WELCOME", "COMMUNITY", "CONTENT", "VOICE CHANNELS"]
    }',
    'completed',
    CURRENT_TIMESTAMP - INTERVAL '2 days'
),
(
    2, 
    2, 
    'Fitness Coach', 
    'Adults (25-35)', 
    'Strength training and nutrition guidance',
    '["Welcome Channel", "General Discussion", "Content Sharing", "Voice Chat Rooms", "Private VIP Area"]',
    'Want separate areas for beginners and advanced members',
    '{
        "channels": [
            {"name": "welcome", "type": "text", "position": 0, "isPublic": true, "description": "Welcome fitness enthusiasts"},
            {"name": "daily-motivation", "type": "text", "position": 1, "isPublic": true, "description": "Daily motivation and tips"},
            {"name": "workout-sharing", "type": "text", "position": 2, "isPublic": true, "description": "Share your workouts"},
            {"name": "nutrition-corner", "type": "text", "position": 3, "isPublic": true, "description": "Nutrition advice and meal prep"},
            {"name": "progress-pics", "type": "text", "position": 4, "isPublic": true, "description": "Share your transformation"},
            {"name": "vip-coaching", "type": "text", "position": 5, "isPublic": false, "description": "Exclusive coaching content"},
            {"name": "voice-workouts", "type": "voice", "position": 6, "isPublic": true, "description": "Group workout sessions"}
        ],
        "welcomeMessage": "💪 Welcome to our fitness family! Whether you are just starting or are a seasoned athlete, this is your space to grow, share, and motivate each other on your fitness journey.",
        "rules": [
            "Support and encourage all fitness levels",
            "No body shaming or negative comments",
            "Share knowledge and experiences respectfully",
            "Keep content appropriate and family-friendly"
        ],
        "categories": ["WELCOME", "FITNESS", "NUTRITION", "VOICE CHANNELS"]
    }',
    'completed',
    CURRENT_TIMESTAMP - INTERVAL '1 day'
),
(
    1, 
    3, 
    'Artist/Designer', 
    'Mixed Audience', 
    'Digital art tutorials and portfolio showcases',
    '["Welcome Channel", "Content Sharing", "Feedback/Reviews", "Resources/Links", "Voice Chat Rooms"]',
    'Need critique channels and resource sharing for artists',
    '{
        "channels": [
            {"name": "welcome", "type": "text", "position": 0, "isPublic": true, "description": "Welcome to our art community"},
            {"name": "showcase", "type": "text", "position": 1, "isPublic": true, "description": "Show off your latest artwork"},
            {"name": "feedback-critique", "type": "text", "position": 2, "isPublic": true, "description": "Constructive feedback on artwork"},
            {"name": "tutorials-resources", "type": "text", "position": 3, "isPublic": true, "description": "Helpful tutorials and resources"},
            {"name": "commissions", "type": "text", "position": 4, "isPublic": true, "description": "Commission opportunities and requests"},
            {"name": "off-topic", "type": "text", "position": 5, "isPublic": true, "description": "Non-art related chat"},
            {"name": "creative-voice", "type": "voice", "position": 6, "isPublic": true, "description": "Voice chat for artists"}
        ],
        "welcomeMessage": "🎨 Welcome to our creative community! This is a space for artists of all levels to share work, get feedback, learn new techniques, and connect with fellow creatives.",
        "rules": [
            "Give constructive feedback, not just criticism",
            "Respect all art styles and skill levels",
            "Credit original artists when sharing others work",
            "Keep commission discussions in the appropriate channel"
        ],
        "categories": ["WELCOME", "ARTWORK", "RESOURCES", "COMMUNITY", "VOICE CHANNELS"]
    }',
    'pending',
    NULL
)
ON CONFLICT DO NOTHING;

-- Sample deployment channels for completed deployments
INSERT INTO deployment_channels (deployment_id, discord_channel_id, channel_name, channel_type, category_name, position, is_public) VALUES
-- Channels for first deployment (Tech Community)
(1, '******************', 'welcome', 'text', 'WELCOME', 0, TRUE),
(1, '555555555555555555', 'announcements', 'announcement', 'WELCOME', 1, TRUE),
(1, '666666666666666666', 'general-chat', 'text', 'COMMUNITY', 2, TRUE),
(1, '777777777777777777', 'tech-discussions', 'text', 'CONTENT', 3, TRUE),
(1, '888888888888888888', 'video-feedback', 'text', 'CONTENT', 4, TRUE),
(1, '999999999999999999', 'voice-lounge', 'voice', 'VOICE CHANNELS', 5, TRUE),

-- Channels for second deployment (Fitness Community)
(2, '111111111111111112', 'welcome', 'text', 'WELCOME', 0, TRUE),
(2, '111111111111111113', 'daily-motivation', 'text', 'FITNESS', 1, TRUE),
(2, '111111111111111114', 'workout-sharing', 'text', 'FITNESS', 2, TRUE),
(2, '111111111111111115', 'nutrition-corner', 'text', 'NUTRITION', 3, TRUE),
(2, '111111111111111116', 'progress-pics', 'text', 'FITNESS', 4, TRUE),
(2, '111111111111111117', 'vip-coaching', 'text', 'FITNESS', 5, FALSE),
(2, '111111111111111118', 'voice-workouts', 'voice', 'VOICE CHANNELS', 6, TRUE)
ON CONFLICT DO NOTHING;

-- Add some statistics views for analytics
CREATE OR REPLACE VIEW deployment_stats AS
SELECT 
    creator_type,
    COUNT(*) as total_deployments,
    COUNT(CASE WHEN deployment_status = 'completed' THEN 1 END) as completed_deployments,
    COUNT(CASE WHEN deployment_status = 'pending' THEN 1 END) as pending_deployments,
    AVG(CASE WHEN deployed_at IS NOT NULL THEN 
        EXTRACT(EPOCH FROM (deployed_at - created_at))/60 
    END) as avg_deployment_time_minutes
FROM deployments
GROUP BY creator_type;

CREATE OR REPLACE VIEW popular_features AS
SELECT 
    feature,
    COUNT(*) as usage_count
FROM deployments,
LATERAL jsonb_array_elements_text(features) as feature
GROUP BY feature
ORDER BY usage_count DESC;

COMMENT ON VIEW deployment_stats IS 'Statistics about deployments by creator type';
COMMENT ON VIEW popular_features IS 'Most popular features requested by users';
