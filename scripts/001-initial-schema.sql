-- Initial database schema for Discord Community Builder
-- This script creates the necessary tables for user management and server configurations

-- Users table to store user information and OAuth data
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    discord_id VARCHAR(255) UNIQUE NOT NULL,
    username VA<PERSON>HAR(255) NOT NULL,
    discriminator VARCHAR(10),
    avatar VARCHAR(255),
    email VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Guilds table to store Discord server information
CREATE TABLE IF NOT EXISTS guilds (
    id SERIAL PRIMARY KEY,
    discord_guild_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    icon VARCHAR(255),
    owner_id VARCHAR(255) NOT NULL,
    permissions INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User-Guild relationships (many-to-many)
CREATE TABLE IF NOT EXISTS user_guilds (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    guild_id INTEGER REFERENCES guilds(id) ON DELETE CASCADE,
    permissions INTEGER DEFAULT 0,
    is_owner BOOLEAN DEFAULT FALSE,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, guild_id)
);

-- Deployment configurations
CREATE TABLE IF NOT EXISTS deployments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    guild_id INTEGER REFERENCES guilds(id) ON DELETE CASCADE,
    creator_type VARCHAR(100) NOT NULL,
    audience_type VARCHAR(100) NOT NULL,
    content_style TEXT,
    features JSONB DEFAULT '[]',
    custom_requirements TEXT,
    generated_config JSONB NOT NULL,
    deployment_status VARCHAR(50) DEFAULT 'pending',
    deployed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Deployment channels (for tracking created channels)
CREATE TABLE IF NOT EXISTS deployment_channels (
    id SERIAL PRIMARY KEY,
    deployment_id INTEGER REFERENCES deployments(id) ON DELETE CASCADE,
    discord_channel_id VARCHAR(255) NOT NULL,
    channel_name VARCHAR(255) NOT NULL,
    channel_type VARCHAR(50) NOT NULL,
    category_name VARCHAR(255),
    position INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_discord_id ON users(discord_id);
CREATE INDEX IF NOT EXISTS idx_guilds_discord_guild_id ON guilds(discord_guild_id);
CREATE INDEX IF NOT EXISTS idx_user_guilds_user_id ON user_guilds(user_id);
CREATE INDEX IF NOT EXISTS idx_user_guilds_guild_id ON user_guilds(guild_id);
CREATE INDEX IF NOT EXISTS idx_deployments_user_id ON deployments(user_id);
CREATE INDEX IF NOT EXISTS idx_deployments_guild_id ON deployments(guild_id);
CREATE INDEX IF NOT EXISTS idx_deployment_channels_deployment_id ON deployment_channels(deployment_id);

-- Insert some sample data for testing
INSERT INTO users (discord_id, username, discriminator, avatar, email) VALUES
('123456789012345678', 'TestCreator', '0001', 'avatar_hash_1', '<EMAIL>'),
('987654321098765432', 'AnotherUser', '0002', 'avatar_hash_2', '<EMAIL>')
ON CONFLICT (discord_id) DO NOTHING;

COMMENT ON TABLE users IS 'Stores Discord user information and OAuth tokens';
COMMENT ON TABLE guilds IS 'Stores Discord server (guild) information';
COMMENT ON TABLE user_guilds IS 'Many-to-many relationship between users and guilds';
COMMENT ON TABLE deployments IS 'Stores deployment configurations and status';
COMMENT ON TABLE deployment_channels IS 'Tracks channels created during deployments';
