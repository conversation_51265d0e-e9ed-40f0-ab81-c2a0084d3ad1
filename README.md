# Discord AI 社区构建器

这是一个AI驱动的Discord社区构建工具，可以根据创作者类型和受众自动生成和部署Discord服务器结构。

## 功能特点

- 基于创作者类型和受众生成定制化的Discord频道结构
- AI生成的欢迎消息和社区规则
- 一键部署到您的Discord服务器
- 智能聊天助手帮助解答问题
- 避免重复创建已存在的频道

## 安装指南

1. 克隆仓库
```bash
git clone https://github.com/yourusername/discord-deployer.git
cd discord-deployer
```

2. 安装依赖
```bash
pnpm install
```

3. 配置环境变量
在项目根目录创建`.env.local`文件，并添加以下配置：

```
# Discord 配置
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_REDIRECT_URI=http://localhost:3000/api/auth/discord/callback

# OpenRouter 配置 (用于AI生成)
OPENROUTER_API_KEY=your_openrouter_api_key
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 应用配置
NEXT_PUBLIC_DISCORD_CLIENT_ID=your_discord_client_id
```

## Discord 应用设置

1. 前往 [Discord Developer Portal](https://discord.com/developers/applications)
2. 创建一个新应用
3. 在"OAuth2"设置中，添加重定向URL：`http://localhost:3000/api/auth/discord/callback`
4. 在"Bot"标签页，创建一个机器人并获取令牌
5. 确保机器人有以下权限：
   - `bot`
   - `applications.commands`
   - `guilds`
   - `identify`

## 本地开发

```bash
pnpm dev
```

访问 http://localhost:3000 即可使用应用。

## 部署

应用可以部署到Vercel或其他支持Next.js的平台：

```bash
pnpm build
```

确保在部署环境中也设置了相同的环境变量。

## 技术栈

- Next.js
- React
- Discord.js
- OpenRouter API (DeepSeek模型)
- TailwindCSS
- Shadcn UI

## 使用的AI模型

默认情况下，该应用使用OpenRouter提供的DeepSeek Chat模型（`deepseek/deepseek-chat-v3-0324:free`）来生成Discord服务器配置。如果您想使用其他模型，可以在`app/api/generate/route.ts`文件中修改以下部分：

```javascript
body: JSON.stringify({
  model: "deepseek/deepseek-chat-v3-0324:free", // 在此处修改模型名称
  messages: [
    // ...
  ],
  // ...
})
```

OpenRouter支持多种模型，您可以根据需要选择，例如：
- `anthropic/claude-3-haiku`
- `anthropic/claude-3-opus`
- `openai/gpt-4o`
- `meta-llama/llama-3-70b-instruct`

更多可用模型请参考[OpenRouter模型列表](https://openrouter.ai/docs#models)。
