{"name": "deploy_discord_server", "description": "Deploy a Discord server configuration based on creator type, audience, and requirements", "parameters": {"type": "object", "properties": {"guild_id": {"type": "string", "description": "The Discord guild ID where the configuration should be deployed"}, "creator_info": {"type": "object", "properties": {"type": {"type": "string", "enum": ["YouTuber", "Instagram Influencer", "TikTok Creator", "Photographer", "Fitness Coach", "Artist", "<PERSON>ian", "Educator", "Other"]}, "audience": {"type": "string", "enum": ["Young Adults", "Adults", "Professionals", "Students", "Hobbyists", "Fans", "Learning Community", "Mixed"]}, "content_style": {"type": "string", "description": "Description of the creator's content style and niche"}}, "required": ["type", "audience", "content_style"]}, "channels": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Channel name (without # prefix)"}, "type": {"type": "string", "enum": ["text", "voice", "announcement"], "description": "Type of Discord channel"}, "category": {"type": "string", "description": "Category this channel belongs to"}, "position": {"type": "integer", "description": "Position order in the channel list"}, "is_public": {"type": "boolean", "description": "Whether the channel is visible to all members"}, "description": {"type": "string", "description": "Channel topic/description"}, "permissions": {"type": "object", "properties": {"read_only": {"type": "boolean", "description": "If true, only admins can post messages"}, "vip_only": {"type": "boolean", "description": "If true, only VIP members can access"}}}}, "required": ["name", "type", "position", "is_public"]}}, "categories": {"type": "array", "items": {"type": "string"}, "description": "List of channel categories to organize channels"}, "welcome_message": {"type": "string", "description": "Welcome message for new members, can include Discord markdown and channel mentions"}, "rules": {"type": "array", "items": {"type": "string"}, "description": "List of community rules"}, "features": {"type": "object", "properties": {"auto_roles": {"type": "boolean", "description": "Whether to set up automatic role assignment"}, "moderation": {"type": "boolean", "description": "Whether to enable moderation features"}, "events": {"type": "boolean", "description": "Whether to create event scheduling channels"}, "content_sharing": {"type": "boolean", "description": "Whether to include content sharing channels"}}}}, "required": ["guild_id", "creator_info", "channels", "welcome_message"]}}