"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { MessageCircle, HelpCircle } from "lucide-react";
import { Chat } from "@/app/components/chat";

export function HelpFooter() {
  const [chatOpen, setChatOpen] = useState(false);
  const [chatError, setChatError] = useState(false);
  
  // 组件加载时延迟打开聊天
  useEffect(() => {
    const timer = setTimeout(() => {
      setChatOpen(true);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  const handleSendMessage = async (message: string) => {
    try {
      setChatError(false);
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });
      
      if (!response.ok) {
        console.error(`聊天API错误: ${response.status}`);
        setChatError(true);
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Error calling chat API:', error);
      setChatError(true);
      return "抱歉，我暂时无法回应您的问题。请稍后再试。";
    }
  };
  
  return (
    <div className="fixed bottom-4 right-4 flex flex-col items-end">
      {chatOpen && (
        <div className="mb-4 w-80 md:w-96">
          <Chat 
            title="AI 助手" 
            description="有任何问题可以直接咨询我"
            onSendMessage={handleSendMessage}
          />
        </div>
      )}
      <div className="flex gap-2">
        <Button 
          variant={chatError ? "destructive" : "outline"}
          size="sm" 
          className={`${chatError ? "" : "bg-white shadow-md border-gray-200"} flex items-center gap-2`}
          onClick={() => setChatOpen(!chatOpen)}
        >
          <MessageCircle className="h-4 w-4 text-purple-600" />
          <span>{chatOpen ? "关闭聊天" : "AI助手"}</span>
        </Button>
        <Link href="/help">
          <Button variant="outline" size="sm" className="bg-white shadow-md border-gray-200 flex items-center gap-2">
            <HelpCircle className="h-4 w-4 text-purple-600" />
            <span>帮助中心</span>
          </Button>
        </Link>
      </div>
    </div>
  );
}
