import Link from "next/link";
import { Bot, HelpCircle } from "lucide-react";

export function AppNavbar() {
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Bot className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4 text-xl font-medium text-gray-900">
              AI Discord社区构建器
            </div>
          </div>
          <nav className="flex space-x-4">
            <Link 
              href="/" 
              className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
            >
              首页
            </Link>
            <Link 
              href="/help" 
              className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center gap-1"
            >
              <HelpCircle className="h-4 w-4" />
              <span>帮助</span>
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}
