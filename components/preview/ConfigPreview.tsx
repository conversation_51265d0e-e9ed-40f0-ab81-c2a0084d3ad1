"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Hash, Volume2, Megaphone, Users, MessageSquare, Settings, Eye, EyeOff } from "lucide-react";
import { GeneratedConfig, ChannelAnalysisResult } from "@/lib/types";

interface ConfigPreviewProps {
  config: GeneratedConfig;
  channelAnalysis?: ChannelAnalysisResult | null;
  useRecommendation?: boolean;
  onToggleRecommendation?: () => void;
  onDeploy: () => void;
  isDeploying: boolean;
}

export function ConfigPreview({ 
  config, 
  channelAnalysis, 
  useRecommendation, 
  onToggleRecommendation,
  onDeploy, 
  isDeploying 
}: ConfigPreviewProps) {
  const [showPrivateChannels, setShowPrivateChannels] = useState(true);

  const getChannelIcon = (type: string) => {
    switch (type) {
      case "voice":
        return <Volume2 className="h-4 w-4" />;
      case "announcement":
        return <Megaphone className="h-4 w-4" />;
      default:
        return <Hash className="h-4 w-4" />;
    }
  };

  const displayConfig = useRecommendation && channelAnalysis?.recommendation 
    ? channelAnalysis.recommendation 
    : config;

  const visibleChannels = showPrivateChannels 
    ? displayConfig.channels 
    : displayConfig.channels.filter(ch => ch.isPublic);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Discord服务器预览</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPrivateChannels(!showPrivateChannels)}
            >
              {showPrivateChannels ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showPrivateChannels ? "隐藏私有频道" : "显示私有频道"}
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          以下是为你生成的Discord服务器结构预览
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="structure" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="structure">频道结构</TabsTrigger>
            <TabsTrigger value="welcome">欢迎消息</TabsTrigger>
            <TabsTrigger value="rules">社区规则</TabsTrigger>
          </TabsList>

          <TabsContent value="structure" className="space-y-4">
            {/* 频道分析结果 */}
            {channelAnalysis && (
              <Card className="border-blue-200 bg-blue-50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    智能分析结果
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-green-700">匹配频道</div>
                      <div className="text-green-600">{channelAnalysis.analysis.exactMatches.length}</div>
                    </div>
                    <div>
                      <div className="font-medium text-blue-700">新增频道</div>
                      <div className="text-blue-600">{channelAnalysis.analysis.newChannels.length}</div>
                    </div>
                    <div>
                      <div className="font-medium text-orange-700">相似频道</div>
                      <div className="text-orange-600">{channelAnalysis.analysis.similarMatches.length}</div>
                    </div>
                    <div>
                      <div className="font-medium text-purple-700">匹配率</div>
                      <div className="text-purple-600">{Math.round(channelAnalysis.analysis.matchRate * 100)}%</div>
                    </div>
                  </div>
                  
                  {onToggleRecommendation && (
                    <div className="mt-4">
                      <Button
                        variant={useRecommendation ? "default" : "outline"}
                        size="sm"
                        onClick={onToggleRecommendation}
                      >
                        {useRecommendation ? "使用推荐配置" : "使用原始配置"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* 频道列表 */}
            <div className="space-y-3">
              {displayConfig.categories.map((category, categoryIndex) => (
                <div key={categoryIndex} className="space-y-2">
                  <div className="flex items-center gap-2 font-medium text-gray-700">
                    <Users className="h-4 w-4" />
                    {category}
                  </div>
                  <div className="ml-6 space-y-1">
                    {visibleChannels
                      .filter(channel => {
                        // 简单的分类逻辑，实际应用中可能需要更复杂的匹配
                        const channelName = channel.name.toLowerCase();
                        const categoryName = category.toLowerCase();
                        
                        if (categoryName.includes('welcome') || categoryName.includes('欢迎')) {
                          return channelName.includes('welcome') || channelName.includes('rule') || channelName.includes('announcement');
                        } else if (categoryName.includes('voice') || categoryName.includes('语音')) {
                          return channel.type === 'voice';
                        } else if (categoryName.includes('content') || categoryName.includes('内容')) {
                          return channelName.includes('content') || channelName.includes('sharing') || channelName.includes('showcase');
                        }
                        return true; // 默认显示在当前分类
                      })
                      .slice(0, 5) // 限制每个分类显示的频道数量
                      .map((channel, channelIndex) => (
                        <div key={channelIndex} className="flex items-center gap-2 text-sm">
                          {getChannelIcon(channel.type)}
                          <span className={channel.isPublic ? "text-gray-700" : "text-gray-500"}>
                            {channel.name}
                          </span>
                          {!channel.isPublic && (
                            <Badge variant="secondary" className="text-xs">私有</Badge>
                          )}
                          {channel.description && (
                            <span className="text-xs text-gray-400 truncate max-w-[200px]">
                              - {channel.description}
                            </span>
                          )}
                        </div>
                      ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="text-sm text-gray-500 mt-4">
              总计: {visibleChannels.length} 个频道
              {!showPrivateChannels && displayConfig.channels.some(ch => !ch.isPublic) && (
                <span> (已隐藏私有频道)</span>
              )}
            </div>
          </TabsContent>

          <TabsContent value="welcome" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  欢迎消息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap text-sm">
                  {displayConfig.welcomeMessage}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rules" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  社区规则
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {displayConfig.rules.map((rule, index) => (
                    <div key={index} className="flex gap-3 text-sm">
                      <span className="font-medium text-purple-600">{index + 1}.</span>
                      <span>{rule}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-6 pt-4 border-t">
          <Button 
            onClick={onDeploy} 
            disabled={isDeploying}
            className="w-full"
            size="lg"
          >
            {isDeploying ? "正在部署..." : "部署到Discord"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
