"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export function EnvironmentCheck() {
  const [missingVars, setMissingVars] = useState<string[]>([]);
  
  useEffect(() => {
    const checkEnvVariables = async () => {
      try {
        // 使用API端点检查服务器端环境变量
        const response = await fetch('/api/check-env');
        if (response.ok) {
          const data = await response.json();
          setMissingVars(data.missingVars || []);
        } else {
          // 如果API调用失败，检查客户端可用的变量
          checkClientEnvVars();
        }
      } catch (error) {
        console.error("Error checking environment variables:", error);
        // 回退到仅检查客户端变量
        checkClientEnvVars();
      }
    };
    
    const checkClientEnvVars = () => {
      const missing: string[] = [];
      
      // 检查NEXT_PUBLIC前缀的环境变量
      if (!process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID) {
        missing.push('NEXT_PUBLIC_DISCORD_CLIENT_ID');
      }
      
      // 添加用户可能需要设置的其他环境变量的提示
      missing.push('可能需要设置: DISCORD_CLIENT_SECRET, DISCORD_BOT_TOKEN, OPENROUTER_API_KEY');
      
      setMissingVars(missing);
    };
    
    checkEnvVariables();
  }, []);
  
  // 如果没有缺失的变量，不显示任何内容
  if (missingVars.length === 0) {
    return null;
  }
  
  return (
    <Card className="border-red-300 mb-6">
      <CardHeader>
        <CardTitle className="text-red-600">⚠️ 配置缺失</CardTitle>
        <CardDescription>
          应用需要以下环境变量才能正常工作。请参考README.md设置.env.local文件。
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="list-disc pl-6 space-y-1 text-gray-700">
          {missingVars.map((varName, index) => (
            <li key={index}>{varName}</li>
          ))}
        </ul>
        <div className="mt-4 p-3 bg-gray-50 rounded text-sm">
          <p>
            在项目根目录创建 <code className="bg-gray-200 px-1 rounded">.env.local</code> 文件并添加必要的环境变量。
          </p>
          <p className="mt-2">
            注意：现在使用的是OpenRouter的DeepSeek模型，需要OPENROUTER_API_KEY而非OPENAI_API_KEY。
          </p>
          <p className="mt-2">
            更多详情请参考{" "}
            <a 
              href="https://github.com/yourusername/discord-deployer" 
              className="text-blue-600 hover:underline"
            >
              README.md
            </a>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
