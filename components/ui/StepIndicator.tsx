"use client";

import { Check } from "lucide-react";
import { StepType } from "@/lib/types";

interface Step {
  id: StepType;
  name: string;
  description: string;
}

interface StepIndicatorProps {
  currentStep: StepType;
  onStepClick?: (step: StepType) => void;
}

const steps: Step[] = [
  {
    id: "intro",
    name: "开始",
    description: "选择配置方式"
  },
  {
    id: "config", 
    name: "配置",
    description: "设置社区参数"
  },
  {
    id: "preview",
    name: "预览",
    description: "查看生成结果"
  },
  {
    id: "deploy",
    name: "部署",
    description: "发布到Discord"
  }
];

export function StepIndicator({ currentStep, onStepClick }: StepIndicatorProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  
  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStepIndex) return "completed";
    if (stepIndex === currentStepIndex) return "current";
    return "upcoming";
  };

  return (
    <nav aria-label="Progress" className="mb-8">
      <ol className="flex items-center justify-center space-x-4 md:space-x-8">
        {steps.map((step, stepIndex) => {
          const status = getStepStatus(stepIndex);
          
          return (
            <li key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <button
                  onClick={() => onStepClick?.(step.id)}
                  disabled={status === "upcoming"}
                  className={`
                    flex h-10 w-10 items-center justify-center rounded-full border-2 text-sm font-medium
                    ${status === "completed" 
                      ? "bg-purple-600 border-purple-600 text-white hover:bg-purple-700" 
                      : status === "current"
                      ? "border-purple-600 text-purple-600 bg-white"
                      : "border-gray-300 text-gray-500 bg-white cursor-not-allowed"
                    }
                    ${onStepClick && status !== "upcoming" ? "hover:scale-105 transition-transform" : ""}
                  `}
                >
                  {status === "completed" ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    <span>{stepIndex + 1}</span>
                  )}
                </button>
                <div className="mt-2 text-center">
                  <div className={`text-sm font-medium ${
                    status === "current" ? "text-purple-600" : 
                    status === "completed" ? "text-gray-900" : "text-gray-500"
                  }`}>
                    {step.name}
                  </div>
                  <div className="text-xs text-gray-500 hidden md:block">
                    {step.description}
                  </div>
                </div>
              </div>
              
              {stepIndex < steps.length - 1 && (
                <div className={`
                  hidden md:block w-16 h-0.5 ml-4
                  ${stepIndex < currentStepIndex ? "bg-purple-600" : "bg-gray-300"}
                `} />
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}
