"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader2, SendIcon } from "lucide-react";
import { DeploymentConfig, ChatMessage, CHAT_WIZARD_STEPS } from "@/lib/types";

interface ChatWizardProps {
  onComplete: (config: DeploymentConfig) => void;
}

export function ChatWizard({ onComplete }: ChatWizardProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      role: 'assistant',
      content: '👋 你好！我是AI助手，将帮助你创建完美的Discord社区。我会问你几个问题来了解你的需求。准备好了吗？'
    }
  ]);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isThinking, setIsThinking] = useState(false);
  const [collectedData, setCollectedData] = useState<Partial<DeploymentConfig>>({
    features: []
  });
  
  const handleSendMessage = async (userMessage: string) => {
    if (isThinking) return;
    
    // 添加用户消息
    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setIsThinking(true);
    
    // 处理当前步骤的回答
    const currentField = CHAT_WIZARD_STEPS[currentStep].field;
    
    // 更新收集的数据
    if (currentField === "features") {
      // 对于features，我们需要解析多个选项
      try {
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            message: `Extract features from this text and return them as a comma-separated list. Text: "${userMessage}"` 
          }),
        });
        
        if (response.ok) {
          const data = await response.json();
          const featuresText = data.response;
          // 分割为数组
          const featuresArray = featuresText.split(/,|、/).map((item: string) => item.trim()).filter(Boolean);
          
          setCollectedData(prev => ({
            ...prev,
            features: featuresArray
          }));
        }
      } catch (error) {
        console.error("Error parsing features:", error);
        // 如果解析失败，直接使用用户输入
        setCollectedData(prev => ({
          ...prev,
          features: [userMessage]
        }));
      }
    } else {
      // 对于其他字段，直接使用用户输入
      setCollectedData(prev => ({
        ...prev,
        [currentField]: userMessage
      }));
    }
    
    // 判断是否进入下一步
    if (currentStep < CHAT_WIZARD_STEPS.length - 1) {
      // 还有下一步
      setTimeout(() => {
        // 添加助手回复
        setMessages(prev => [...prev, { 
          role: 'assistant', 
          content: `谢谢！我已记录下你的回答。\n\n${CHAT_WIZARD_STEPS[currentStep + 1].question}` 
        }]);
        setCurrentStep(prev => prev + 1);
        setIsThinking(false);
      }, 1000);
    } else {
      // 最后一步，完成收集
      setTimeout(async () => {
        // 添加最终确认消息
        setMessages(prev => [...prev, { 
          role: 'assistant', 
          content: "太棒了！我已收集完所有信息。正在为你生成Discord社区配置..." 
        }]);
        
        // 调用完成回调
        onComplete(collectedData as DeploymentConfig);
        setIsThinking(false);
      }, 1500);
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Discord社区创建向导</CardTitle>
        <CardDescription>
          通过对话方式，我将帮助你创建完美的Discord社区
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[400px] overflow-y-auto flex flex-col space-y-4 p-4">
        {messages.map((msg, index) => (
          <div 
            key={index} 
            className={`flex ${msg.role === 'assistant' ? 'justify-start' : 'justify-end'}`}
          >
            <div 
              className={`max-w-[80%] rounded-lg px-4 py-2 ${
                msg.role === 'assistant' 
                  ? 'bg-gray-100 text-gray-900' 
                  : 'bg-purple-600 text-white'
              }`}
            >
              {msg.content.split('\n').map((line, i) => (
                <p key={i} className={i > 0 ? 'mt-2' : ''}>{line}</p>
              ))}
            </div>
          </div>
        ))}
        {isThinking && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-900 rounded-lg px-4 py-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse"></div>
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <form 
          onSubmit={(e) => {
            e.preventDefault();
            const form = e.target as HTMLFormElement;
            const input = form.elements.namedItem('message') as HTMLInputElement;
            if (input.value.trim()) {
              handleSendMessage(input.value);
              input.value = '';
            }
          }}
          className="flex w-full gap-2"
        >
          <Input 
            name="message" 
            placeholder="输入你的回答..." 
            disabled={isThinking} 
            className="flex-1"
          />
          <Button type="submit" disabled={isThinking}>
            {isThinking ? <Loader2 className="h-4 w-4 animate-spin" /> : <SendIcon className="h-4 w-4" />}
            <span className="sr-only">发送</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
