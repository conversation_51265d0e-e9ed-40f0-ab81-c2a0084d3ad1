"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { SimpleSelect } from "@/components/ui/SimpleSelect";
import { DeploymentConfig, CREATOR_TYPES, AUDIENCE_TYPES, FEATURE_CATEGORIES } from "@/lib/types";

interface ConfigurationFormProps {
  initialConfig?: Partial<DeploymentConfig>;
  onSubmit: (config: DeploymentConfig) => void;
}

export function ConfigurationForm({ initialConfig, onSubmit }: ConfigurationFormProps) {
  const [config, setConfig] = useState<DeploymentConfig>({
    creatorType: initialConfig?.creatorType || "",
    audienceType: initialConfig?.audienceType || "",
    contentStyle: initialConfig?.contentStyle || "",
    features: initialConfig?.features || [],
    customRequirements: initialConfig?.customRequirements || "",
  });

  const handleFeatureToggle = (feature: string, checked: boolean) => {
    setConfig(prev => ({
      ...prev,
      features: checked 
        ? [...prev.features, feature]
        : prev.features.filter(f => f !== feature)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(config);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>配置你的Discord社区</CardTitle>
        <CardDescription>
          填写以下信息，我们将为你生成定制化的Discord服务器结构
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 创作者类型 */}
          <div className="space-y-2">
            <Label htmlFor="creatorType">创作者类型</Label>
            <SimpleSelect
              options={CREATOR_TYPES}
              value={config.creatorType}
              onChange={(value) => setConfig(prev => ({ ...prev, creatorType: value }))}
              placeholder="选择你的创作者类型"
            />
          </div>

          {/* 受众类型 */}
          <div className="space-y-2">
            <Label htmlFor="audienceType">目标受众</Label>
            <SimpleSelect
              options={AUDIENCE_TYPES}
              value={config.audienceType}
              onChange={(value) => setConfig(prev => ({ ...prev, audienceType: value }))}
              placeholder="选择你的目标受众"
            />
          </div>

          {/* 内容风格 */}
          <div className="space-y-2">
            <Label htmlFor="contentStyle">内容风格</Label>
            <Input
              id="contentStyle"
              value={config.contentStyle}
              onChange={(e) => setConfig(prev => ({ ...prev, contentStyle: e.target.value }))}
              placeholder="例如：教育性、娱乐性、激励性、技术性..."
            />
          </div>

          {/* 功能选择 */}
          <div className="space-y-4">
            <Label>社区功能</Label>
            <div className="space-y-4">
              {FEATURE_CATEGORIES.map((category) => (
                <div key={category.name} className="space-y-2">
                  <h4 className="font-medium text-sm text-gray-700">{category.name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {category.features.map((feature) => (
                      <div key={feature} className="flex items-center space-x-2">
                        <Checkbox
                          id={feature}
                          checked={config.features.includes(feature)}
                          onCheckedChange={(checked) => 
                            handleFeatureToggle(feature, checked as boolean)
                          }
                        />
                        <Label 
                          htmlFor={feature} 
                          className="text-sm font-normal cursor-pointer"
                        >
                          {feature}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 自定义需求 */}
          <div className="space-y-2">
            <Label htmlFor="customRequirements">特殊需求或想法</Label>
            <Textarea
              id="customRequirements"
              value={config.customRequirements}
              onChange={(e) => setConfig(prev => ({ ...prev, customRequirements: e.target.value }))}
              placeholder="描述任何特殊需求、想法或你希望在社区中实现的功能..."
              rows={3}
            />
          </div>

          <Button 
            type="submit" 
            className="w-full"
            disabled={!config.creatorType || !config.audienceType}
          >
            生成Discord配置
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
