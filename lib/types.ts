// 应用步骤类型
export type StepType = "intro" | "config" | "preview" | "deploy";

// 步骤常量
export const STEP_INTRO: StepType = "intro";
export const STEP_CONFIG: StepType = "config";
export const STEP_PREVIEW: StepType = "preview";
export const STEP_DEPLOY: StepType = "deploy";

// 部署配置接口
export interface DeploymentConfig {
  creatorType: string;
  audienceType: string;
  contentStyle: string;
  features: string[];
  customRequirements: string;
}

// 生成的配置接口
export interface GeneratedConfig {
  channels: Array<{
    name: string;
    type: "text" | "voice" | "announcement";
    position: number;
    isPublic: boolean;
    description: string;
  }>;
  welcomeMessage: string;
  rules: string[];
  categories: string[];
}

// 频道分析结果接口
export interface ChannelAnalysisResult {
  success: boolean;
  existingStructure: {
    categories: string[];
    channels: any[];
  };
  analysis: {
    exactMatches: string[];
    similarMatches: any[];
    newChannels: string[];
    unusedChannels: string[];
    matchRate: number;
  };
  recommendation: {
    categories: string[];
    channels: any[];
    welcomeMessage: string;
    rules: string[];
  };
}

// 应用状态接口
export interface AppState {
  currentStep: StepType;
  deploymentConfig: DeploymentConfig;
  generatedConfig: GeneratedConfig | null;
  isGenerating: boolean;
  isDeploying: boolean;
  deploymentResult: any;
  deploymentError: string | null;
  channelAnalysis: ChannelAnalysisResult | null;
  isAnalyzing: boolean;
  useRecommendation: boolean;
  isDebug: boolean;
  errorDetails: string | null;
}

// 创作者类型选项
export const CREATOR_TYPES = [
  "YouTuber/Content Creator",
  "Instagram Influencer",
  "TikTok Creator",
  "Artist/Designer",
  "Photographer",
  "Fitness Coach",
  "Musician",
  "Educator/Teacher",
  "Writer/Blogger",
  "Podcast Host",
  "Streamer/Gamer",
  "Digital Marketer",
  "NFT/Crypto Creator",
  "Makeup Artist",
  "Fashion Designer",
  "Dancer/Choreographer",
  "Chef/Food Creator",
  "Travel Blogger",
  "Business Coach",
  "Other",
];

// 受众类型选项
export const AUDIENCE_TYPES = [
  "Young Adults (18-25)",
  "Adults (25-35)",
  "Professionals",
  "Students",
  "Hobbyists",
  "Fans/Followers",
  "Learning Community",
  "Creative Professionals",
  "Entrepreneurs",
  "Tech Enthusiasts",
  "Wellness Enthusiasts",
  "Gaming Community",
  "Fashion Enthusiasts",
  "Parents",
  "Seniors",
  "International Audience",
  "Industry Experts",
  "Brand Partners",
  "Mixed Audience",
];

// 可用功能选项
export const AVAILABLE_FEATURES = [
  "Welcome Channel",
  "General Discussion",
  "Q&A Section",
  "Announcements",
  "Voice Chat Rooms",
  "Content Sharing",
  "Feedback/Reviews",
  "Private VIP Area",
  "Events/Activities",
  "Resources/Links",
  "Collaboration Space",
  "Portfolio Showcase",
  "Tutorial/Learning Area",
  "Bot Commands",
  "Music Listening",
  "Member Introductions",
  "Community Challenges",
  "Live Stream Discussions",
  "Mentorship Program",
  "Job Opportunities",
  "Regional/Language Channels",
  "Support Ticket System",
  "Marketplace/Trading",
  "Meme/Fun Zone",
];

// 按类别分组的功能
export const FEATURE_CATEGORIES = [
  {
    name: "基础频道",
    features: [
      "Welcome Channel",
      "General Discussion",
      "Announcements",
      "Q&A Section",
      "Voice Chat Rooms",
    ]
  },
  {
    name: "内容相关",
    features: [
      "Content Sharing",
      "Portfolio Showcase",
      "Feedback/Reviews",
      "Tutorial/Learning Area",
      "Resources/Links",
    ]
  },
  {
    name: "社区互动",
    features: [
      "Member Introductions",
      "Community Challenges",
      "Events/Activities",
      "Live Stream Discussions",
      "Meme/Fun Zone",
    ]
  },
  {
    name: "专业功能",
    features: [
      "Private VIP Area",
      "Collaboration Space",
      "Mentorship Program",
      "Job Opportunities",
      "Marketplace/Trading",
      "Support Ticket System",
      "Bot Commands",
      "Music Listening",
      "Regional/Language Channels",
    ]
  }
];

// 聊天向导步骤
export const CHAT_WIZARD_STEPS = [
  {
    question: "你是什么类型的创作者？例如：YouTuber、音乐人、艺术家、教育工作者等。",
    field: "creatorType"
  },
  {
    question: "你的主要受众是谁？例如：年轻人、专业人士、学生、粉丝等。",
    field: "audienceType"
  },
  {
    question: "描述一下你的内容风格。例如：教育性、娱乐性、激励性等。",
    field: "contentStyle"
  },
  {
    question: "你希望在Discord社区中包含哪些功能？可以多选，例如：欢迎频道、问答区、语音聊天、内容分享等。",
    field: "features"
  },
  {
    question: "有任何特殊需求或想法吗？",
    field: "customRequirements"
  }
];

// 消息类型
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}
