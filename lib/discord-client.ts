import { Client, GatewayIntentBits, ChannelType, PermissionFlagsBits } from 'discord.js';

// 单例Discord客户端
class DiscordClientManager {
  private static instance: DiscordClientManager;
  private client: Client | null = null;
  private isReady: boolean = false;
  private isConnecting: boolean = false;

  private constructor() {}

  public static getInstance(): DiscordClientManager {
    if (!DiscordClientManager.instance) {
      DiscordClientManager.instance = new DiscordClientManager();
    }
    return DiscordClientManager.instance;
  }

  public async getClient(): Promise<Client> {
    if (this.client && this.isReady) {
      return this.client;
    }

    if (this.isConnecting) {
      // 等待连接完成
      return new Promise((resolve, reject) => {
        const checkReady = () => {
          if (this.isReady && this.client) {
            resolve(this.client);
          } else if (!this.isConnecting) {
            reject(new Error('Discord client connection failed'));
          } else {
            setTimeout(checkReady, 100);
          }
        };
        checkReady();
      });
    }

    return this.initializeClient();
  }

  private async initializeClient(): Promise<Client> {
    if (!process.env.DISCORD_BOT_TOKEN) {
      throw new Error('DISCORD_BOT_TOKEN is not configured');
    }

    this.isConnecting = true;

    try {
      this.client = new Client({
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMessages,
          GatewayIntentBits.MessageContent,
        ],
      });

      this.client.once('ready', () => {
        console.log(`Discord bot logged in as ${this.client?.user?.tag}`);
        this.isReady = true;
        this.isConnecting = false;
      });

      this.client.on('error', (error) => {
        console.error('Discord client error:', error);
        this.isReady = false;
        this.isConnecting = false;
      });

      this.client.on('disconnect', () => {
        console.log('Discord client disconnected');
        this.isReady = false;
      });

      await this.client.login(process.env.DISCORD_BOT_TOKEN);
      
      // 等待ready事件
      if (!this.isReady) {
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Discord client ready timeout'));
          }, 10000);

          this.client?.once('ready', () => {
            clearTimeout(timeout);
            resolve(void 0);
          });
        });
      }

      return this.client;
    } catch (error) {
      this.isConnecting = false;
      this.isReady = false;
      throw error;
    }
  }

  public isClientReady(): boolean {
    return this.isReady && this.client !== null;
  }

  public async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.destroy();
      this.client = null;
      this.isReady = false;
      this.isConnecting = false;
    }
  }
}

// 导出单例实例
export const discordClientManager = DiscordClientManager.getInstance();

// 便捷函数
export async function getDiscordClient(): Promise<Client> {
  return discordClientManager.getClient();
}

export function isDiscordClientReady(): boolean {
  return discordClientManager.isClientReady();
}

// 类型定义
export interface ChannelConfig {
  name: string;
  type: 'text' | 'voice' | 'announcement';
  position: number;
  isPublic: boolean;
  description: string;
}

export interface DeploymentConfig {
  channels: ChannelConfig[];
  welcomeMessage: string;
  rules: string[];
  categories: string[];
}

// Discord相关工具函数
export function getDiscordChannelType(type: string): ChannelType {
  switch (type) {
    case 'text':
      return ChannelType.GuildText;
    case 'voice':
      return ChannelType.GuildVoice;
    case 'announcement':
      return ChannelType.GuildAnnouncement;
    default:
      return ChannelType.GuildText;
  }
}

export function determineChannelCategory(channelName: string, categoryMap: Map<string, string>): string | undefined {
  const name = channelName.toLowerCase();
  
  if (name.includes('welcome') || name.includes('introduction') || name.includes('rules') || name.includes('announcement')) {
    return categoryMap.get('WELCOME');
  } else if (name.includes('voice') || name.includes('audio') || name.includes('chat') || name.includes('talk')) {
    return categoryMap.get('VOICE CHANNELS');
  } else if (name.includes('content') || name.includes('sharing') || name.includes('showcase') || name.includes('portfolio')) {
    return categoryMap.get('CONTENT');
  } else if (name.includes('vip') || name.includes('premium') || name.includes('exclusive')) {
    return categoryMap.get('VIP') || categoryMap.get('COMMUNITY');
  } else if (name.includes('resource') || name.includes('link') || name.includes('tool')) {
    return categoryMap.get('RESOURCES') || categoryMap.get('COMMUNITY');
  } else if (name.includes('feedback') || name.includes('suggestion') || name.includes('request')) {
    return categoryMap.get('FEEDBACK') || categoryMap.get('COMMUNITY');
  } else {
    return categoryMap.get('COMMUNITY');
  }
}
