// 应用错误类型定义
export enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  
  // Discord相关错误
  DISCORD_AUTH_FAILED = 'DISCORD_AUTH_FAILED',
  DISCORD_BOT_NOT_READY = 'DISCORD_BOT_NOT_READY',
  DISCORD_GUILD_NOT_FOUND = 'DISCORD_GUILD_NOT_FOUND',
  DISCORD_INSUFFICIENT_PERMISSIONS = 'DISCORD_INSUFFICIENT_PERMISSIONS',
  DISCORD_RATE_LIMITED = 'DISCORD_RATE_LIMITED',
  DISCORD_CHANNEL_EXISTS = 'DISCORD_CHANNEL_EXISTS',
  
  // AI服务错误
  AI_SERVICE_UNAVAILABLE = 'AI_SERVICE_UNAVAILABLE',
  AI_RESPONSE_INVALID = 'AI_RESPONSE_INVALID',
  AI_QUOTA_EXCEEDED = 'AI_QUOTA_EXCEEDED',
  
  // 配置错误
  MISSING_ENV_VARS = 'MISSING_ENV_VARS',
  INVALID_CONFIG = 'INVALID_CONFIG',
  
  // 用户错误
  USER_NOT_AUTHENTICATED = 'USER_NOT_AUTHENTICATED',
  USER_INSUFFICIENT_PERMISSIONS = 'USER_INSUFFICIENT_PERMISSIONS',
}

// 自定义应用错误类
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;

    // 确保堆栈跟踪正确
    Error.captureStackTrace(this, this.constructor);
  }

  // 创建特定类型错误的静态方法
  static validation(message: string, context?: Record<string, any>): AppError {
    return new AppError(message, ErrorCode.VALIDATION_ERROR, 400, true, context);
  }

  static discord(message: string, code: ErrorCode, context?: Record<string, any>): AppError {
    const statusCode = code === ErrorCode.DISCORD_INSUFFICIENT_PERMISSIONS ? 403 : 500;
    return new AppError(message, code, statusCode, true, context);
  }

  static ai(message: string, code: ErrorCode, context?: Record<string, any>): AppError {
    const statusCode = code === ErrorCode.AI_QUOTA_EXCEEDED ? 429 : 500;
    return new AppError(message, code, statusCode, true, context);
  }

  static auth(message: string, context?: Record<string, any>): AppError {
    return new AppError(message, ErrorCode.USER_NOT_AUTHENTICATED, 401, true, context);
  }

  static notFound(message: string, context?: Record<string, any>): AppError {
    return new AppError(message, ErrorCode.DISCORD_GUILD_NOT_FOUND, 404, true, context);
  }

  // 转换为API响应格式
  toJSON() {
    return {
      error: {
        message: this.message,
        code: this.code,
        statusCode: this.statusCode,
        context: this.context,
      },
    };
  }
}

// 错误处理工具函数
export function handleError(error: unknown): AppError {
  if (error instanceof AppError) {
    return error;
  }

  if (error instanceof Error) {
    // Discord.js 错误处理
    if (error.message.includes('Missing Permissions')) {
      return AppError.discord(
        '机器人缺少必要的权限来执行此操作',
        ErrorCode.DISCORD_INSUFFICIENT_PERMISSIONS,
        { originalError: error.message }
      );
    }

    if (error.message.includes('Unknown Guild')) {
      return AppError.notFound(
        '找不到指定的Discord服务器',
        { originalError: error.message }
      );
    }

    if (error.message.includes('Rate limited')) {
      return AppError.discord(
        '操作过于频繁，请稍后再试',
        ErrorCode.DISCORD_RATE_LIMITED,
        { originalError: error.message }
      );
    }

    // 网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return new AppError(
        '网络连接错误，请检查网络连接',
        ErrorCode.NETWORK_ERROR,
        500,
        true,
        { originalError: error.message }
      );
    }

    // 通用错误
    return new AppError(
      error.message,
      ErrorCode.UNKNOWN_ERROR,
      500,
      true,
      { originalError: error.message }
    );
  }

  // 未知错误类型
  return new AppError(
    '发生了未知错误',
    ErrorCode.UNKNOWN_ERROR,
    500,
    false,
    { originalError: String(error) }
  );
}

// 日志记录函数
export function logError(error: AppError | Error, context?: Record<string, any>) {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    message: error.message,
    stack: error.stack,
    context,
  };

  if (error instanceof AppError) {
    errorInfo.code = error.code;
    errorInfo.statusCode = error.statusCode;
    errorInfo.isOperational = error.isOperational;
    errorInfo.context = { ...errorInfo.context, ...error.context };
  }

  console.error('[ERROR]', JSON.stringify(errorInfo, null, 2));
}

// 环境变量验证
export function validateEnvironmentVariables(): void {
  const requiredVars = [
    'DISCORD_BOT_TOKEN',
    'DISCORD_CLIENT_ID',
    'DISCORD_CLIENT_SECRET',
    'OPENROUTER_API_KEY',
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new AppError(
      `缺少必要的环境变量: ${missingVars.join(', ')}`,
      ErrorCode.MISSING_ENV_VARS,
      500,
      true,
      { missingVars }
    );
  }
}

// 用户友好的错误消息映射
export const ERROR_MESSAGES: Record<ErrorCode, string> = {
  [ErrorCode.UNKNOWN_ERROR]: '发生了未知错误，请稍后重试',
  [ErrorCode.VALIDATION_ERROR]: '输入的信息有误，请检查后重试',
  [ErrorCode.NETWORK_ERROR]: '网络连接错误，请检查网络连接',
  
  [ErrorCode.DISCORD_AUTH_FAILED]: 'Discord授权失败，请重新授权',
  [ErrorCode.DISCORD_BOT_NOT_READY]: 'Discord机器人未就绪，请稍后重试',
  [ErrorCode.DISCORD_GUILD_NOT_FOUND]: '找不到指定的Discord服务器',
  [ErrorCode.DISCORD_INSUFFICIENT_PERMISSIONS]: '机器人权限不足，请检查权限设置',
  [ErrorCode.DISCORD_RATE_LIMITED]: '操作过于频繁，请稍后再试',
  [ErrorCode.DISCORD_CHANNEL_EXISTS]: '频道已存在，跳过创建',
  
  [ErrorCode.AI_SERVICE_UNAVAILABLE]: 'AI服务暂时不可用，请稍后重试',
  [ErrorCode.AI_RESPONSE_INVALID]: 'AI响应格式错误，请重新生成',
  [ErrorCode.AI_QUOTA_EXCEEDED]: 'AI服务配额已用完，请稍后重试',
  
  [ErrorCode.MISSING_ENV_VARS]: '服务配置不完整，请联系管理员',
  [ErrorCode.INVALID_CONFIG]: '配置信息无效，请重新配置',
  
  [ErrorCode.USER_NOT_AUTHENTICATED]: '用户未认证，请先登录',
  [ErrorCode.USER_INSUFFICIENT_PERMISSIONS]: '用户权限不足',
};

// 获取用户友好的错误消息
export function getUserFriendlyMessage(error: AppError): string {
  return ERROR_MESSAGES[error.code] || error.message;
}
