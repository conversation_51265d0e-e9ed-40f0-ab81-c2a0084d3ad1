"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Copy, Check, ExternalLink, AlertTriangle, Info } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function SetupGuide() {
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    clientId: "",
    clientSecret: "",
    botToken: "",
    serviceToken: "",
  })

  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text)
    setCopiedField(field)
    setTimeout(() => setCopiedField(null), 2000)
  }

  const generateServiceToken = () => {
    const token = Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
    setFormData((prev) => ({ ...prev, serviceToken: token }))
  }

  const generateEnvFile = () => {
    const envContent = `# Discord 配置
DISCORD_CLIENT_ID=${formData.clientId}
DISCORD_CLIENT_SECRET=${formData.clientSecret}
NEXT_PUBLIC_DISCORD_CLIENT_ID=${formData.clientId}
DISCORD_BOT_TOKEN=${formData.botToken}

# 应用程序配置
NEXT_PUBLIC_BASE_URL=http://localhost:3000
BOT_SERVICE_URL=http://localhost:3001
BOT_SERVICE_TOKEN=${formData.serviceToken}
PORT=3001

# 数据库配置（可选）
# DATABASE_URL=postgresql://username:password@localhost:5432/discord_builder`

    copyToClipboard(envContent, "env-file")
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Discord Bot 设置指南</h1>
        <p className="text-gray-600">按照以下步骤配置你的 Discord 应用程序和 Bot</p>
      </div>

      {/* Step 1: Discord Application */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              1
            </span>
            创建 Discord 应用程序
          </CardTitle>
          <CardDescription>在 Discord 开发者门户创建新的应用程序</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
            <ExternalLink className="w-4 h-4 text-blue-600" />
            <a
              href="https://discord.com/developers/applications"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              打开 Discord Developer Portal
            </a>
          </div>

          <div className="space-y-3">
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold">操作步骤：</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 mt-2">
                <li>点击 "New Application" 按钮</li>
                <li>输入应用程序名称（例如："My Community Builder"）</li>
                <li>点击 "Create" 创建应用程序</li>
                <li>在 "General Information" 页面复制 Application ID</li>
              </ol>
            </div>

            <div>
              <Label htmlFor="client-id">Application ID (Client ID)</Label>
              <div className="flex gap-2">
                <Input
                  id="client-id"
                  placeholder="粘贴你的 Application ID"
                  value={formData.clientId}
                  onChange={(e) => setFormData((prev) => ({ ...prev, clientId: e.target.value }))}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(formData.clientId, "client-id")}
                  disabled={!formData.clientId}
                >
                  {copiedField === "client-id" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 2: OAuth2 Setup */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              2
            </span>
            配置 OAuth2
          </CardTitle>
          <CardDescription>设置客户端密钥和重定向 URL</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>客户端密钥只会显示一次，请立即复制并保存！</AlertDescription>
          </Alert>

          <div className="border-l-4 border-orange-500 pl-4">
            <h4 className="font-semibold">操作步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 mt-2">
              <li>点击左侧菜单的 "OAuth2" → "General"</li>
              <li>在 "Client Secret" 部分，点击 "Reset Secret"</li>
              <li>立即复制生成的密钥</li>
              <li>在 "Redirects" 部分添加重定向 URL</li>
            </ol>
          </div>

          <div>
            <Label htmlFor="client-secret">Client Secret</Label>
            <div className="flex gap-2">
              <Input
                id="client-secret"
                type="password"
                placeholder="粘贴你的 Client Secret"
                value={formData.clientSecret}
                onChange={(e) => setFormData((prev) => ({ ...prev, clientSecret: e.target.value }))}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formData.clientSecret, "client-secret")}
                disabled={!formData.clientSecret}
              >
                {copiedField === "client-secret" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">重定向 URL 配置：</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <code className="bg-white px-2 py-1 rounded">http://localhost:3000/api/auth/discord/callback</code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard("http://localhost:3000/api/auth/discord/callback", "redirect-local")}
                >
                  {copiedField === "redirect-local" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
              <p className="text-gray-600">本地开发环境使用</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 3: Bot Creation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              3
            </span>
            创建 Discord Bot
          </CardTitle>
          <CardDescription>创建 Bot 并获取 Token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>Bot Token 非常敏感，不要分享给任何人！</AlertDescription>
          </Alert>

          <div className="border-l-4 border-green-500 pl-4">
            <h4 className="font-semibold">操作步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 mt-2">
              <li>在同一个应用程序中，点击左侧菜单的 "Bot"</li>
              <li>点击 "Add Bot" 按钮</li>
              <li>确认创建 Bot</li>
              <li>在 "Token" 部分，点击 "Reset Token"</li>
              <li>立即复制生成的 token</li>
            </ol>
          </div>

          <div>
            <Label htmlFor="bot-token">Bot Token</Label>
            <div className="flex gap-2">
              <Input
                id="bot-token"
                type="password"
                placeholder="粘贴你的 Bot Token"
                value={formData.botToken}
                onChange={(e) => setFormData((prev) => ({ ...prev, botToken: e.target.value }))}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formData.botToken, "bot-token")}
                disabled={!formData.botToken}
              >
                {copiedField === "bot-token" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">Bot 权限配置：</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-green-600">✅</span>
                <span>Message Content Intent</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-600">🔵</span>
                <span>Server Members Intent（可选）</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 4: Service Token */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              4
            </span>
            生成服务令牌
          </CardTitle>
          <CardDescription>为 Bot 服务生成安全令牌</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>这个令牌用于保护你的 Bot 服务 API，确保只有授权的请求才能执行部署操作。</AlertDescription>
          </Alert>

          <div>
            <Label htmlFor="service-token">Bot Service Token</Label>
            <div className="flex gap-2">
              <Input
                id="service-token"
                placeholder="点击生成按钮创建安全令牌"
                value={formData.serviceToken}
                onChange={(e) => setFormData((prev) => ({ ...prev, serviceToken: e.target.value }))}
              />
              <Button onClick={generateServiceToken}>生成令牌</Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formData.serviceToken, "service-token")}
                disabled={!formData.serviceToken}
              >
                {copiedField === "service-token" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 5: Generate .env file */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              5
            </span>
            生成环境配置文件
          </CardTitle>
          <CardDescription>生成完整的 .env.local 文件内容</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={generateEnvFile}
              disabled={!formData.clientId || !formData.clientSecret || !formData.botToken || !formData.serviceToken}
              className="flex-1"
            >
              {copiedField === "env-file" ? (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  已复制到剪贴板
                </>
              ) : (
                "生成并复制 .env.local 文件"
              )}
            </Button>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              将复制的内容保存为项目根目录下的 <code>.env.local</code> 文件
            </AlertDescription>
          </Alert>

          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">下一步：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
              <li>
                在项目根目录创建 <code>.env.local</code> 文件
              </li>
              <li>粘贴上面生成的内容</li>
              <li>
                运行 <code>npm install</code> 安装依赖
              </li>
              <li>
                运行 <code>npm run dev</code> 启动前端
              </li>
              <li>
                运行 <code>npm run bot</code> 启动 Bot 服务
              </li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
