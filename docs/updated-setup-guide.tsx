"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Copy, Check, ExternalLink, AlertTriangle, Info, Eye, EyeOff } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function UpdatedSetupGuide() {
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})
  const [formData, setFormData] = useState({
    clientId: "1379659157422215279", // Pre-filled from user's env
    clientSecret: "",
    botToken: "",
    serviceToken: "",
  })

  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text)
    setCopiedField(field)
    setTimeout(() => setC<PERSON>ied<PERSON>ield(null), 2000)
  }

  const toggleSecretVisibility = (field: string) => {
    setShowSecrets((prev) => ({ ...prev, [field]: !prev[field] }))
  }

  const generateServiceToken = () => {
    const token = Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
    setFormData((prev) => ({ ...prev, serviceToken: token }))
  }

  const generateEnvFile = () => {
    const envContent = `# Discord Bot Configuration
DISCORD_BOT_TOKEN=${formData.botToken}
DISCORD_CLIENT_ID=${formData.clientId}
DISCORD_CLIENT_SECRET=${formData.clientSecret}

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_DISCORD_CLIENT_ID=${formData.clientId}

# Bot Service Configuration
BOT_SERVICE_URL=http://localhost:3001
BOT_SERVICE_TOKEN=${formData.serviceToken}

# Database (Optional - for production)
DATABASE_URL=your_database_connection_string_here

# Security
NEXTAUTH_SECRET=${formData.serviceToken}
NEXTAUTH_URL=http://localhost:3000`

    copyToClipboard(envContent, "env-file")
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Discord Bot 设置指南 (2024最新版)</h1>
        <p className="text-gray-600">根据最新的 Discord Developer Portal 界面更新的设置指南</p>
      </div>

      {/* Current Status */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800">当前配置状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span>Application ID (Client ID): {formData.clientId}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-red-600">❌</span>
              <span>Client Secret: 需要重新获取（当前值不正确）</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-red-600">❌</span>
              <span>Bot Token: 需要获取</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 1: Get Client Secret */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-orange-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              1
            </span>
            获取 Client Secret
          </CardTitle>
          <CardDescription>从 OAuth2 页面获取客户端密钥</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              你的 .env.local 文件中的 DISCORD_CLIENT_SECRET 值看起来像是 Bot Token，这是不正确的。Client Secret 和 Bot
              Token 是不同的。
            </AlertDescription>
          </Alert>

          <div className="border-l-4 border-orange-500 pl-4">
            <h4 className="font-semibold">操作步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 mt-2">
              <li>
                在你的应用程序页面，点击左侧菜单的 <strong>"OAuth2"</strong>
              </li>
              <li>
                在 OAuth2 页面，找到 <strong>"Client Secret"</strong> 部分
              </li>
              <li>
                点击 <strong>"Reset Secret"</strong> 按钮
              </li>
              <li>立即复制显示的密钥（只显示一次！）</li>
              <li>在 "Redirects" 部分添加重定向 URL</li>
            </ol>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">需要添加的重定向 URL：</h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <code className="bg-white px-2 py-1 rounded text-sm">
                  http://localhost:3000/api/auth/discord/callback
                </code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard("http://localhost:3000/api/auth/discord/callback", "redirect-local")}
                >
                  {copiedField === "redirect-local" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>

          <div>
            <Label htmlFor="client-secret">Client Secret</Label>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  id="client-secret"
                  type={showSecrets.clientSecret ? "text" : "password"}
                  placeholder="粘贴从 OAuth2 页面获取的 Client Secret"
                  value={formData.clientSecret}
                  onChange={(e) => setFormData((prev) => ({ ...prev, clientSecret: e.target.value }))}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility("clientSecret")}
                >
                  {showSecrets.clientSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formData.clientSecret, "client-secret")}
                disabled={!formData.clientSecret}
              >
                {copiedField === "client-secret" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 2: Create Bot and Get Token */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              2
            </span>
            创建 Bot 并获取 Token
          </CardTitle>
          <CardDescription>在 Bot 页面创建 Bot 并获取 Token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-l-4 border-green-500 pl-4">
            <h4 className="font-semibold">操作步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 mt-2">
              <li>
                点击左侧菜单的 <strong>"Bot"</strong>
              </li>
              <li>
                如果还没有创建 Bot，点击 <strong>"Add Bot"</strong>
              </li>
              <li>
                在 "Token" 部分，点击 <strong>"Reset Token"</strong>
              </li>
              <li>立即复制生成的 Token（只显示一次！）</li>
              <li>向下滚动到 "Privileged Gateway Intents"</li>
              <li>
                启用 <strong>"Message Content Intent"</strong>
              </li>
            </ol>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>Bot Token 以 "MTM..." 或类似格式开头，非常敏感，不要分享给任何人！</AlertDescription>
          </Alert>

          <div>
            <Label htmlFor="bot-token">Bot Token</Label>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  id="bot-token"
                  type={showSecrets.botToken ? "text" : "password"}
                  placeholder="粘贴从 Bot 页面获取的 Bot Token"
                  value={formData.botToken}
                  onChange={(e) => setFormData((prev) => ({ ...prev, botToken: e.target.value }))}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => toggleSecretVisibility("botToken")}
                >
                  {showSecrets.botToken ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formData.botToken, "bot-token")}
                disabled={!formData.botToken}
              >
                {copiedField === "bot-token" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">重要的 Bot 设置：</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-green-600">✅</span>
                <span>
                  <strong>Message Content Intent</strong> - 必须启用
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-600">🔵</span>
                <span>
                  <strong>Server Members Intent</strong> - 可选
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-600">🔵</span>
                <span>
                  <strong>Presence Intent</strong> - 可选
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 3: Generate Service Token */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              3
            </span>
            生成服务令牌
          </CardTitle>
          <CardDescription>为内部服务通信生成安全令牌</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="service-token">Bot Service Token</Label>
            <div className="flex gap-2">
              <Input id="service-token" placeholder="点击生成按钮创建安全令牌" value={formData.serviceToken} readOnly />
              <Button onClick={generateServiceToken}>生成令牌</Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formData.serviceToken, "service-token")}
                disabled={!formData.serviceToken}
              >
                {copiedField === "service-token" ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 4: Generate Complete .env file */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              4
            </span>
            生成完整的环境配置
          </CardTitle>
          <CardDescription>生成正确的 .env.local 文件内容</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={generateEnvFile}
              disabled={!formData.clientSecret || !formData.botToken || !formData.serviceToken}
              className="flex-1"
            >
              {copiedField === "env-file" ? (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  已复制到剪贴板
                </>
              ) : (
                "生成并复制完整的 .env.local 文件"
              )}
            </Button>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              复制生成的内容，替换你现有的 <code>.env.local</code> 文件内容
            </AlertDescription>
          </Alert>

          {formData.clientSecret && formData.botToken && formData.serviceToken && (
            <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">✅ 配置完成！</h4>
              <p className="text-green-700 text-sm">所有必需的值都已填写完成。点击上面的按钮复制完整的环境配置文件。</p>
            </div>
          )}

          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">下一步：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
              <li>
                用生成的内容替换你的 <code>.env.local</code> 文件
              </li>
              <li>确保文件保存在项目根目录</li>
              <li>
                重启开发服务器：<code>npm run dev</code>
              </li>
              <li>
                在新终端启动 Bot 服务：<code>npm run bot</code>
              </li>
              <li>
                访问 <code>http://localhost:3000</code> 测试应用
              </li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Quick Links */}
      <Card>
        <CardHeader>
          <CardTitle>快速链接</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a
              href="https://discord.com/developers/applications"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <ExternalLink className="w-4 h-4 text-blue-600" />
              <span className="text-blue-600">Discord Developer Portal</span>
            </a>
            <a
              href="https://discord.com/developers/docs/intro"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <ExternalLink className="w-4 h-4 text-purple-600" />
              <span className="text-purple-600">Discord API 文档</span>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
