// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { Client, GatewayIntentBits, ChannelType, PermissionFlagsBits } = require("discord.js")
const express = require("express")
const cors = require("cors")

// Enhanced logging
const logInfo = (message) => {
  console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
};

const logError = (message, error) => {
  console.error(`[ERROR] ${new Date().toISOString()}: ${message}`);
  if (error) {
    if (error.stack) {
      console.error(error.stack);
    } else {
      console.error(error);
    }
  }
};

const app = express()
app.use(cors())
app.use(express.json())

// Add request logging middleware
app.use((req, res, next) => {
  logInfo(`${req.method} ${req.path}`);
  next();
});

const client = new Client({
  intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages, GatewayIntentBits.MessageContent],
})

client.once("ready", () => {
  logInfo(`Bot logged in as ${client.user.tag}`)
})

// Listen for error events
client.on('error', (error) => {
  logError('Discord client error:', error);
});

// Middleware to verify bot service token
const verifyToken = (req, res, next) => {
  const token = req.headers.authorization?.replace("Bearer ", "")
  if (token !== process.env.BOT_SERVICE_TOKEN) {
    logError(`Unauthorized access attempt with token: ${token?.substring(0, 10)}...`);
    return res.status(401).json({ error: "Unauthorized" })
  }
  next()
}

// Helper function to determine the best category for a channel
const determineChannelCategory = (channelConfig, categoryMap) => {
  const name = channelConfig.name.toLowerCase();
  
  // Map channel names to appropriate categories using a more comprehensive approach
  if (name.includes('welcome') || name.includes('introduction') || name.includes('rules') || name.includes('announcement')) {
    return categoryMap.get('WELCOME');
  } else if (name.includes('voice') || name.includes('audio') || name.includes('chat') || name.includes('talk')) {
    return categoryMap.get('VOICE CHANNELS');
  } else if (name.includes('content') || name.includes('sharing') || name.includes('showcase') || name.includes('portfolio')) {
    return categoryMap.get('CONTENT');
  } else if (name.includes('vip') || name.includes('premium') || name.includes('exclusive')) {
    return categoryMap.get('VIP') || categoryMap.get('COMMUNITY');
  } else if (name.includes('resource') || name.includes('link') || name.includes('tool')) {
    return categoryMap.get('RESOURCES') || categoryMap.get('COMMUNITY');
  } else if (name.includes('feedback') || name.includes('suggestion') || name.includes('request')) {
    return categoryMap.get('FEEDBACK') || categoryMap.get('COMMUNITY');
  } else {
    return categoryMap.get('COMMUNITY');
  }
};

// Rate limiting to avoid Discord API rate limits
const createWithRateLimit = async (createFn, retryCount = 3, delay = 500) => {
  let attempts = 0;
  
  while (attempts < retryCount) {
    try {
      return await createFn();
    } catch (error) {
      attempts++;
      if (error.code === 429) { // Rate limit error
        const retryAfter = error.retry_after || delay;
        logInfo(`Rate limited. Waiting ${retryAfter}ms before retry. Attempt ${attempts}/${retryCount}`);
        await new Promise(resolve => setTimeout(resolve, retryAfter));
      } else if (attempts >= retryCount) {
        throw error; // Rethrow if we've exhausted retries
      } else {
        logInfo(`Error occurred, retrying. Attempt ${attempts}/${retryCount}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw new Error(`Failed after ${retryCount} attempts`);
};

app.post("/deploy", verifyToken, async (req, res) => {
  try {
    const { guildId, config, userConfig } = req.body
    
    logInfo(`Received deployment request for guild ID: ${guildId}`);
    logInfo(`Configuration: ${JSON.stringify(config.categories)}`);

    try {
      const guild = await client.guilds.fetch(guildId)
      if (!guild) {
        logError(`Guild not found: ${guildId}`);
        return res.status(404).json({ error: "Guild not found" })
      }

      logInfo(`Deploying configuration for guild: ${guild.name} (ID: ${guild.id})`);
      
      // Verify bot permissions
      const botMember = await guild.members.fetch(client.user.id);
      logInfo(`Bot permissions in guild: ${botMember.permissions.toArray().join(', ')}`);
      
      if (!botMember.permissions.has(PermissionFlagsBits.ManageChannels)) {
        logError(`Bot lacks required permissions in guild ${guild.name}`);
        return res.status(403).json({ error: "Bot lacks required permissions", required: "MANAGE_CHANNELS" });
      }

      // Check for existing channels to avoid duplicates
      const existingChannels = await guild.channels.fetch();
      const existingNames = Array.from(existingChannels.values()).map(channel => channel.name.toLowerCase());
      
      logInfo(`Found ${existingNames.length} existing channels`);

      // Create categories first
      const categoryMap = new Map()
      for (const categoryName of config.categories) {
        try {
          // Skip if a similar category already exists
          const similarExists = existingNames.some(name => 
            name.includes(categoryName.toLowerCase()) || 
            categoryName.toLowerCase().includes(name)
          );
          
          if (similarExists) {
            logInfo(`Similar category to "${categoryName}" already exists, skipping creation`);
            // Find the existing category to use
            const existingCategory = Array.from(existingChannels.values())
              .find(c => c.type === ChannelType.GuildCategory && 
                (c.name.toLowerCase().includes(categoryName.toLowerCase()) || 
                 categoryName.toLowerCase().includes(c.name.toLowerCase())));
                 
            if (existingCategory) {
              categoryMap.set(categoryName, existingCategory.id);
              logInfo(`Using existing category: ${existingCategory.name} (ID: ${existingCategory.id})`);
            }
            continue;
          }
          
          logInfo(`Creating category: ${categoryName}`);
          const category = await createWithRateLimit(async () => {
            return await guild.channels.create({
              name: categoryName,
              type: ChannelType.GuildCategory,
            });
          });
          
          categoryMap.set(categoryName, category.id)
          logInfo(`Created category: ${categoryName} (ID: ${category.id})`)
        } catch (error) {
          logError(`Failed to create category ${categoryName}:`, error)
        }
      }

      // Create channels
      const createdChannels = []
      for (const [index, channelConfig] of config.channels.entries()) {
        try {
          // Skip if a channel with the same name already exists
          if (existingNames.includes(channelConfig.name.toLowerCase())) {
            logInfo(`Channel "${channelConfig.name}" already exists, skipping creation`);
            continue;
          }
          
          const channelType =
            channelConfig.type === "text"
              ? ChannelType.GuildText
              : channelConfig.type === "voice"
                ? ChannelType.GuildVoice
                : ChannelType.GuildAnnouncement

          // Determine parent category using the helper function
          const parentId = determineChannelCategory(channelConfig, categoryMap);
          
          logInfo(`Creating channel: ${channelConfig.name} (type: ${channelConfig.type}, parent: ${parentId})`);

          const channelOptions = {
            name: channelConfig.name,
            type: channelType,
            position: channelConfig.position,
            parent: parentId,
            topic: channelConfig.description,
          }

          // Set permissions for private channels
          if (!channelConfig.isPublic) {
            channelOptions.permissionOverwrites = [
              {
                id: guild.roles.everyone.id,
                deny: [PermissionFlagsBits.ViewChannel],
              },
            ]
          }

          const channel = await createWithRateLimit(async () => {
            return await guild.channels.create(channelOptions);
          });
          
          createdChannels.push({
            name: channel.name,
            id: channel.id,
            type: channelConfig.type,
          })

          logInfo(`Created channel: #${channel.name} (ID: ${channel.id})`)
          
          // Add a small delay between channel creations to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (error) {
          logError(`Failed to create channel ${channelConfig.name}:`, error)
        }
      }

      // Send welcome message to the first text channel (usually #welcome or #general)
      const welcomeChannel = createdChannels.find(
        (ch) => ch.type === "text" && (ch.name.includes("welcome") || ch.name.includes("general")),
      )

      if (welcomeChannel && config.welcomeMessage) {
        try {
          logInfo(`Sending welcome message to channel: ${welcomeChannel.name}`);
          const channel = await guild.channels.fetch(welcomeChannel.id)
          if (channel && channel.isTextBased()) {
            await channel.send(config.welcomeMessage)
            logInfo("Welcome message sent")
          }
        } catch (error) {
          logError("Failed to send welcome message:", error)
        }
      }

      // Create rules channel and post rules
      if (config.rules && config.rules.length > 0) {
        try {
          // Check if rules channel already exists
          const existingRulesChannel = Array.from(existingChannels.values())
            .find(c => c.name.toLowerCase() === 'rules');
            
          let rulesChannel;
          
          if (existingRulesChannel) {
            logInfo(`Using existing rules channel: ${existingRulesChannel.name}`);
            rulesChannel = existingRulesChannel;
          } else {
            logInfo(`Creating rules channel`);
            rulesChannel = await createWithRateLimit(async () => {
              return await guild.channels.create({
                name: "rules",
                type: ChannelType.GuildText,
                parent: categoryMap.get("WELCOME"),
                permissionOverwrites: [
                  {
                    id: guild.roles.everyone.id,
                    deny: [PermissionFlagsBits.SendMessages],
                    allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.ReadMessageHistory],
                  },
                ],
              });
            });
          }

          const rulesMessage = `📋 **Community Rules**\n\n${config.rules.map((rule, index) => `${index + 1}. ${rule}`).join("\n\n")}\n\n*By participating in this server, you agree to follow these rules.*`

          await rulesChannel.send(rulesMessage)
          logInfo("Rules posted to rules channel")
        } catch (error) {
          logError("Failed to create rules channel:", error)
        }
      }

      // Add setup completion channel
      try {
        const setupChannel = await createWithRateLimit(async () => {
          return await guild.channels.create({
            name: "setup-complete",
            type: ChannelType.GuildText,
            parent: categoryMap.get("WELCOME"),
            permissionOverwrites: [
              {
                id: guild.roles.everyone.id,
                deny: [PermissionFlagsBits.SendMessages],
                allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.ReadMessageHistory],
              },
            ],
          });
        });
        
        const setupMessage = `## 🎉 Server Setup Complete!

This server has been automatically configured by Discord Community Builder.

**Configuration Details:**
- Creator Type: ${userConfig.creatorType}
- Target Audience: ${userConfig.audienceType}
- Content Style: ${userConfig.contentStyle}
- Channels Created: ${createdChannels.length}

For more help, visit: https://discord.com/developers/docs/resources/guild`;

        await setupChannel.send(setupMessage);
        logInfo("Setup complete channel created and message posted");
        
        // Add to created channels list
        createdChannels.push({
          name: setupChannel.name,
          id: setupChannel.id,
          type: "text",
        });
        
      } catch (error) {
        logError("Failed to create setup complete channel:", error);
      }

      res.json({
        success: true,
        message: "Discord server deployed successfully",
        channels: createdChannels,
        guildName: guild.name,
        guildId: guild.id,
      })
    } catch (guildError) {
      logError("Error processing guild:", guildError);
      return res.status(500).json({ 
        error: guildError.message,
        errorCode: guildError.code || 'UNKNOWN_ERROR'
      });
    }
  } catch (error) {
    logError("Deployment error:", error)
    res.status(500).json({ error: error.message })
  }
})

app.get("/health", (req, res) => {
  res.json({ 
    status: "Bot is running", 
    user: client.user?.tag,
    guilds: client.guilds.cache.size,
    uptime: Math.floor(client.uptime / 1000) + " seconds"
  })
})

const PORT = process.env.PORT || 3001
app.listen(PORT, () => {
  logInfo(`Bot service running on port ${PORT}`)
})

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN).catch(error => {
  logError("Failed to login to Discord:", error);
});

module.exports = { client, app }
